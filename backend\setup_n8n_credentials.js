const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🔧 Configurando credenciales en n8n...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Crear credenciales de PostgreSQL
async function createPostgresCredentials() {
  console.log('🗄️ Creando credenciales de PostgreSQL...');
  
  const credentialData = {
    name: 'PostgreSQL Local',
    type: 'postgres',
    data: {
      host: 'postgres',
      port: 5432,
      database: 'ccjapdb',
      user: 'ccjapuser',
      password: 'ccjappassword',
      ssl: 'disable'
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/credentials', credentialData);
    console.log(`✅ Credenciales PostgreSQL creadas con ID: ${result.id}`);
    return result.id;
  } catch (error) {
    if (error.message.includes('already exists') || error.message.includes('duplicate')) {
      console.log('⚠️ Las credenciales PostgreSQL ya existen');
      return null;
    }
    throw error;
  }
}

// Obtener credenciales existentes
async function getCredentials() {
  console.log('📋 Obteniendo credenciales existentes...');
  try {
    const credentials = await makeN8nRequest('GET', '/credentials');
    return credentials.data || credentials;
  } catch (error) {
    console.log('⚠️ No se pudieron obtener credenciales:', error.message);
    return [];
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Obtener credenciales existentes
    const existingCredentials = await getCredentials();
    console.log(`📊 Credenciales existentes: ${existingCredentials.length}`);
    
    if (existingCredentials.length > 0) {
      console.log('\n📋 Credenciales encontradas:');
      existingCredentials.forEach((cred, index) => {
        console.log(`   ${index + 1}. ${cred.name} (${cred.type}) - ID: ${cred.id}`);
      });
    }
    
    // Verificar si ya existe credencial de PostgreSQL
    const postgresCredential = existingCredentials.find(cred => 
      cred.type === 'postgres' && cred.name.includes('PostgreSQL')
    );
    
    if (postgresCredential) {
      console.log(`✅ Credencial PostgreSQL ya existe: ${postgresCredential.name} (${postgresCredential.id})`);
    } else {
      // Crear credenciales de PostgreSQL
      await createPostgresCredentials();
    }
    
    console.log('\n🎉 ¡Configuración de credenciales completada!');
    console.log('\n📝 Próximos pasos:');
    console.log('1. Acceder a n8n: http://localhost:5678');
    console.log('2. Verificar que las credenciales estén configuradas');
    console.log('3. Editar los workflows para usar las credenciales correctas');
    console.log('4. Ejecutar pruebas: node test_ai_system.js');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
