const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🤖 Creando workflow con IA REAL (ChatGPT + Whisper + Memoria)...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow con IA real, memoria y transcripción
async function createRealAIWorkflow() {
  console.log('\n🧠 Creando workflow con ChatGPT, Whisper y Memoria...');
  
  const workflowData = {
    name: 'WhatsApp IA REAL - ChatGPT + Memoria + Audio',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir mensajes
      {
        parameters: {
          httpMethod: 'POST',
          path: 'ai-real',
          responseMode: 'responseNode'
        },
        id: 'webhook-ai-real',
        name: 'WhatsApp IA Real',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Verificar tipo de mensaje (texto o audio)
      {
        parameters: {
          conditions: {
            options: {
              caseSensitive: true,
              leftValue: '',
              typeValidation: 'strict'
            },
            conditions: [
              {
                id: 'audio-condition',
                leftValue: '={{ $json.type }}',
                rightValue: 'audio',
                operator: {
                  type: 'string',
                  operation: 'equals'
                }
              }
            ],
            combinator: 'and'
          },
          options: {}
        },
        id: 'check-message-type',
        name: 'Es Audio?',
        type: 'n8n-nodes-base.if',
        typeVersion: 2,
        position: [460, 300]
      },
      
      // 3. Transcribir audio con Whisper (rama TRUE)
      {
        parameters: {
          resource: 'audio',
          operation: 'transcribe',
          file: '={{ $json.audio_url }}',
          options: {
            language: 'es',
            prompt: 'Transcripción de mensaje de WhatsApp de padre de familia del Colegio Cristiano Jerusalén. Puede contener reportes de ausencias, consultas sobre horarios, o reportes de comportamiento estudiantil.'
          }
        },
        id: 'transcribe-audio',
        name: 'Transcribir Audio',
        type: 'n8n-nodes-base.openAi',
        typeVersion: 1,
        position: [680, 200],
        credentials: {
          openAiApi: {
            id: 'openai-ccjap',
            name: 'OpenAI CCJAP'
          }
        }
      },
      
      // 4. Preparar texto transcrito
      {
        parameters: {
          jsCode: `
// Preparar mensaje transcrito para IA
const originalData = $('WhatsApp IA Real').first().json;
const transcription = $input.first().json.text;

return [{
  json: {
    from: originalData.from,
    name: originalData.name,
    body: transcription,
    original_type: 'audio',
    transcribed: true,
    timestamp: new Date().toISOString()
  }
}];
`
        },
        id: 'prepare-transcribed',
        name: 'Preparar Transcripción',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 200]
      },
      
      // 5. Preparar mensaje de texto (rama FALSE)
      {
        parameters: {
          jsCode: `
// Preparar mensaje de texto para IA
const data = $input.first().json;

return [{
  json: {
    from: data.from,
    name: data.name,
    body: data.body,
    original_type: 'text',
    transcribed: false,
    timestamp: new Date().toISOString()
  }
}];
`
        },
        id: 'prepare-text',
        name: 'Preparar Texto',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [680, 400]
      },
      
      // 6. Unir ambas ramas
      {
        parameters: {},
        id: 'merge-messages',
        name: 'Unir Mensajes',
        type: 'n8n-nodes-base.merge',
        typeVersion: 3,
        position: [1120, 300]
      },
      
      // 7. Obtener historial de conversación
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.from }}'
            }
          },
          table: 'mensajes_whatsapp',
          limit: 10,
          sort: {
            values: [
              {
                column: 'fecha_recepcion',
                direction: 'DESC'
              }
            ]
          }
        },
        id: 'get-conversation-history',
        name: 'Obtener Historial',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1340, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 8. Preparar contexto para ChatGPT con memoria
      {
        parameters: {
          jsCode: `
// Preparar contexto completo para ChatGPT con memoria
const currentMessage = $('Unir Mensajes').first().json;
const history = $input.all();

// Construir historial de conversación
let conversationHistory = [];
if (history.length > 0) {
  conversationHistory = history.slice(0, 5).map(msg => ({
    role: msg.json.ai_analysis ? 'assistant' : 'user',
    content: msg.json.ai_analysis ? 
      JSON.parse(msg.json.ai_analysis).respuesta_automatica : 
      msg.json.texto_mensaje,
    timestamp: msg.json.fecha_recepcion
  }));
}

// Sistema prompt especializado para el colegio
const systemPrompt = \`Eres el asistente inteligente del Colegio Cristiano Jerusalén de los Altos de Palencia (CCJAP).

INFORMACIÓN DEL COLEGIO:
- Nombre: Colegio Cristiano Jerusalén de los Altos de Palencia
- Horario de clases: 7:00 AM - 12:00 PM (Lunes a Viernes)
- Horario de oficina: 7:00 AM - 3:00 PM (Lunes a Viernes)
- Teléfono: +503 1234-5678
- Ubicación: Palencia, El Salvador

CAPACIDADES PRINCIPALES:
1. AUSENCIAS: Procesar reportes de inasistencia estudiantil
2. CONSULTAS: Responder sobre horarios, actividades, información general
3. REPORTES: Gestionar reportes de comportamiento con evidencia
4. COMUNICACIÓN: Facilitar comunicación entre padres, docentes y dirección

TIPOS DE MENSAJE A CLASIFICAR:
- "ausencia": Reporte de inasistencia estudiantil
- "consulta": Pregunta sobre información del colegio
- "reporte_comportamiento": Reporte de comportamiento con/sin evidencia
- "director_atencion": Requiere intervención urgente del director
- "general": Mensaje general o saludo

INSTRUCCIONES:
- Siempre responde de forma profesional, empática y útil
- Para ausencias: confirma recepción y notifica que se informará al docente
- Para consultas: proporciona información precisa del colegio
- Para reportes: confirma recepción y explica el proceso de revisión
- Mantén un tono cálido pero profesional
- Usa emojis apropiados para hacer la comunicación más amigable
- Si no entiendes algo, pide aclaración de forma cortés

MEMORIA DE CONVERSACIÓN:
\${conversationHistory.length > 0 ? 
  'Historial reciente:\\n' + conversationHistory.map((msg, i) => 
    \`\${i + 1}. [\${new Date(msg.timestamp).toLocaleDateString()}] \${msg.role === 'user' ? 'Padre' : 'Asistente'}: \${msg.content}\`
  ).join('\\n') : 
  'Primera conversación con este contacto.'
}

MENSAJE ACTUAL:
Remitente: \${currentMessage.name} (\${currentMessage.from})
Tipo: \${currentMessage.original_type === 'audio' ? '🎤 Audio transcrito' : '💬 Texto'}
Contenido: "\${currentMessage.body}"

Analiza el mensaje, clasifica el tipo, y proporciona una respuesta apropiada.\`;

return [{
  json: {
    system_prompt: systemPrompt,
    user_message: currentMessage.body,
    sender_info: {
      phone: currentMessage.from,
      name: currentMessage.name,
      message_type: currentMessage.original_type
    },
    conversation_history: conversationHistory,
    timestamp: new Date().toISOString()
  }
}];
`
        },
        id: 'prepare-ai-context',
        name: 'Preparar Contexto IA',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1560, 300]
      },
      
      // 9. ChatGPT con memoria
      {
        parameters: {
          model: 'gpt-4',
          messages: {
            values: [
              {
                role: 'system',
                content: '={{ $json.system_prompt }}'
              },
              {
                role: 'user',
                content: '={{ $json.user_message }}'
              }
            ]
          },
          options: {
            temperature: 0.7,
            maxTokens: 800,
            topP: 1,
            frequencyPenalty: 0,
            presencePenalty: 0
          }
        },
        id: 'chatgpt-analysis',
        name: 'ChatGPT con Memoria',
        type: 'n8n-nodes-base.openAi',
        typeVersion: 1,
        position: [1780, 300],
        credentials: {
          openAiApi: {
            id: 'openai-ccjap',
            name: 'OpenAI CCJAP'
          }
        }
      },
      
      // 10. Procesar respuesta de ChatGPT
      {
        parameters: {
          jsCode: `
// Procesar respuesta de ChatGPT y extraer información
const chatgptResponse = $input.first().json.message.content;
const contextData = $('Preparar Contexto IA').first().json;

// Análisis básico del tipo de mensaje
const mensaje = contextData.user_message.toLowerCase();
let tipoMensaje = 'general';
let confianza = 0.8;
let requiereAprobacion = false;

// Clasificación inteligente
if (mensaje.includes('ausencia') || mensaje.includes('no asistirá') || mensaje.includes('enfermo')) {
  tipoMensaje = 'ausencia';
  confianza = 0.95;
  requiereAprobacion = false;
} else if (mensaje.includes('horario') || mensaje.includes('información') || mensaje.includes('pregunta')) {
  tipoMensaje = 'consulta';
  confianza = 0.9;
  requiereAprobacion = false;
} else if (mensaje.includes('reporte') || mensaje.includes('problema') || mensaje.includes('comportamiento')) {
  tipoMensaje = 'reporte_comportamiento';
  confianza = 0.9;
  requiereAprobacion = true;
} else if (mensaje.includes('urgente') || mensaje.includes('director')) {
  tipoMensaje = 'director_atencion';
  confianza = 0.85;
  requiereAprobacion = true;
}

const analisisCompleto = {
  mensaje_original: contextData.user_message,
  respuesta_chatgpt: chatgptResponse,
  clasificacion: {
    tipo_mensaje: tipoMensaje,
    confianza: confianza,
    requiere_aprobacion: requiereAprobacion
  },
  remitente: contextData.sender_info,
  timestamp: new Date().toISOString(),
  procesado_por: 'chatgpt-4'
};

console.log('🤖 Análisis ChatGPT completado:', JSON.stringify(analisisCompleto, null, 2));

return [{
  json: analisisCompleto
}];
`
        },
        id: 'process-chatgpt-response',
        name: 'Procesar Respuesta ChatGPT',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [2000, 300]
      },
      
      // 11. Guardar en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.remitente.phone }}',
              nombre_remitente: '={{ $json.remitente.name }}',
              texto_mensaje: '={{ $json.mensaje_original }}',
              tipo_mensaje: '={{ $json.clasificacion.tipo_mensaje }}',
              ai_analysis: '={{ JSON.stringify($json) }}',
              confidence_score: '={{ $json.clasificacion.confianza }}',
              requires_approval: '={{ $json.clasificacion.requiere_aprobacion }}',
              response_message: '={{ $json.respuesta_chatgpt }}',
              procesado: true,
              fecha_recepcion: '={{ new Date().toISOString() }}'
            }
          },
          table: 'mensajes_whatsapp'
        },
        id: 'save-to-database',
        name: 'Guardar en BD',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [2220, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 12. Respuesta final
      {
        parameters: {
          options: {}
        },
        id: 'webhook-response',
        name: 'Respuesta Final',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [2440, 300]
      }
    ],
    
    connections: {
      'WhatsApp IA Real': {
        main: [
          [
            {
              node: 'Es Audio?',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Es Audio?': {
        main: [
          [
            {
              node: 'Transcribir Audio',
              type: 'main',
              index: 0
            }
          ],
          [
            {
              node: 'Preparar Texto',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Transcribir Audio': {
        main: [
          [
            {
              node: 'Preparar Transcripción',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Transcripción': {
        main: [
          [
            {
              node: 'Unir Mensajes',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Texto': {
        main: [
          [
            {
              node: 'Unir Mensajes',
              type: 'main',
              index: 1
            }
          ]
        ]
      },
      'Unir Mensajes': {
        main: [
          [
            {
              node: 'Obtener Historial',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Obtener Historial': {
        main: [
          [
            {
              node: 'Preparar Contexto IA',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Contexto IA': {
        main: [
          [
            {
              node: 'ChatGPT con Memoria',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'ChatGPT con Memoria': {
        main: [
          [
            {
              node: 'Procesar Respuesta ChatGPT',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar Respuesta ChatGPT': {
        main: [
          [
            {
              node: 'Guardar en BD',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Guardar en BD': {
        main: [
          [
            {
              node: 'Respuesta Final',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow con IA REAL creado con ID: ${result.id}`);
    
    // Activar el workflow
    await makeN8nRequest('POST', `/workflows/${result.id}/activate`);
    console.log(`✅ Workflow activado automáticamente`);
    
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow con IA real:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow con IA real
    const realAIWorkflowId = await createRealAIWorkflow();
    
    console.log('\n🎉 ¡Workflow con IA REAL creado exitosamente!');
    console.log('📋 Características implementadas:');
    console.log('   ✅ ChatGPT-4 con memoria de conversación');
    console.log('   ✅ Transcripción de audio con Whisper');
    console.log('   ✅ Análisis inteligente de mensajes');
    console.log('   ✅ Clasificación automática por tipo');
    console.log('   ✅ Respuestas contextuales personalizadas');
    console.log('   ✅ Almacenamiento en base de datos');
    
    console.log('\n🔗 URL de Webhook:');
    console.log(`   - IA REAL: ${N8N_BASE_URL}/webhook/ai-real`);
    
    console.log('\n🧪 Ejemplos de prueba:');
    console.log('\n📝 Mensaje de texto:');
    console.log('curl -X POST http://localhost:5678/webhook/ai-real \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"from": "50312345678", "body": "Mi hijo Juan no asistirá hoy", "name": "María", "type": "text"}\'');
    
    console.log('\n🎤 Mensaje de audio:');
    console.log('curl -X POST http://localhost:5678/webhook/ai-real \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"from": "50312345678", "audio_url": "https://example.com/audio.mp3", "name": "María", "type": "audio"}\'');
    
    console.log('\n⚠️ IMPORTANTE:');
    console.log('1. Configurar credencial OpenAI en n8n con tu API key');
    console.log('2. Configurar credencial PostgreSQL en n8n');
    console.log('3. El workflow procesará tanto texto como audio');
    console.log('4. ChatGPT mantendrá memoria de conversaciones');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
