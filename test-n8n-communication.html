<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Comunicación n8n - CCJAP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test de Comunicación n8n - CCJAP</h1>
        <p>Herramienta para verificar la comunicación entre n8n y el sistema CCJAP</p>
        
        <div class="test-section">
            <h3>1. Verificación de Servicios</h3>
            <button onclick="testN8nAccess()">🌐 Probar Acceso a n8n</button>
            <button onclick="testBackendAccess()">🔧 Probar Acceso al Backend</button>
            <button onclick="testDatabaseConnection()">🗄️ Probar Conexión a Base de Datos</button>
            <div id="services-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Comunicación Backend ↔ n8n</h3>
            <button onclick="testN8nFromBackend()">📡 Probar n8n desde Backend</button>
            <button onclick="testBackendFromN8n()">📡 Probar Backend desde n8n</button>
            <div id="communication-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Flujos de Trabajo WhatsApp</h3>
            <button onclick="testWhatsAppWebhook()">📱 Probar Webhook WhatsApp</button>
            <button onclick="testMessageProcessing()">⚙️ Probar Procesamiento de Mensajes</button>
            <button onclick="testAutomatedResponse()">🤖 Probar Respuesta Automática</button>
            <div id="whatsapp-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Configuración n8n</h3>
            <button onclick="checkN8nConfig()">⚙️ Verificar Configuración</button>
            <button onclick="testN8nAPI()">🔑 Probar API de n8n</button>
            <button onclick="importWorkflow()">📥 Importar Flujo de Trabajo</button>
            <div id="config-result" class="result"></div>
        </div>
    </div>

    <script>
        function log(message, resultId, type = 'info') {
            const resultDiv = document.getElementById(resultId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            if (resultDiv.innerHTML === '') {
                resultDiv.innerHTML = logEntry;
            } else {
                resultDiv.innerHTML += logEntry;
            }
            
            resultDiv.className = `result ${type}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        async function testN8nAccess() {
            log('🌐 Probando acceso a n8n...', 'services-result');
            try {
                const response = await fetch('http://localhost:5678/', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                log('✅ n8n es accesible en puerto 5678', 'services-result', 'success');
            } catch (error) {
                log(`❌ Error accediendo a n8n: ${error.message}`, 'services-result', 'error');
            }
        }
        
        async function testBackendAccess() {
            log('🔧 Probando acceso al backend...', 'services-result');
            try {
                const response = await fetch('http://localhost:3001/');
                const text = await response.text();
                log(`✅ Backend responde: ${text}`, 'services-result', 'success');
            } catch (error) {
                log(`❌ Error accediendo al backend: ${error.message}`, 'services-result', 'error');
            }
        }
        
        async function testDatabaseConnection() {
            log('🗄️ Probando conexión a base de datos...', 'services-result');
            try {
                // Hacer login primero
                const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    log('✅ Autenticación exitosa', 'services-result', 'success');
                    
                    // Probar consulta a la base de datos
                    const dbResponse = await fetch('http://localhost:3001/api/usuarios', {
                        headers: { 'Authorization': `Bearer ${loginData.token}` }
                    });
                    
                    if (dbResponse.ok) {
                        const users = await dbResponse.json();
                        log(`✅ Base de datos conectada - ${users.length} usuarios encontrados`, 'services-result', 'success');
                    } else {
                        log('❌ Error consultando base de datos', 'services-result', 'error');
                    }
                } else {
                    log('❌ Error de autenticación', 'services-result', 'error');
                }
            } catch (error) {
                log(`❌ Error de conexión: ${error.message}`, 'services-result', 'error');
            }
        }
        
        async function testN8nFromBackend() {
            log('📡 Probando comunicación Backend → n8n...', 'communication-result');
            try {
                // Simular llamada desde el backend a n8n
                const response = await fetch('http://localhost:3001/api/waapi/test-connection', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + await getAuthToken()
                    },
                    body: JSON.stringify({
                        n8n_url: 'http://localhost:5678',
                        n8n_api_key: 'test-key'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Backend puede comunicarse con n8n: ${data.message}`, 'communication-result', 'success');
                } else {
                    log('❌ Error en comunicación Backend → n8n', 'communication-result', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'communication-result', 'error');
            }
        }
        
        async function testBackendFromN8n() {
            log('📡 Probando comunicación n8n → Backend...', 'communication-result');
            try {
                // Simular webhook desde n8n al backend
                const response = await fetch('http://localhost:3001/api/webhook/whatsapp', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        from: '+50312345678',
                        text: 'Mensaje de prueba desde n8n',
                        timestamp: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ n8n puede enviar datos al backend via webhook', 'communication-result', 'success');
                } else {
                    log('❌ Error en comunicación n8n → Backend', 'communication-result', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'communication-result', 'error');
            }
        }
        
        async function testWhatsAppWebhook() {
            log('📱 Probando webhook de WhatsApp...', 'whatsapp-result');
            try {
                const response = await fetch('http://localhost:3001/api/webhook/whatsapp', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        from: '+50387654321',
                        text: 'Hola, mi hijo no podrá asistir hoy por enfermedad',
                        timestamp: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ Webhook de WhatsApp funcionando correctamente', 'whatsapp-result', 'success');
                } else {
                    log('❌ Error en webhook de WhatsApp', 'whatsapp-result', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'whatsapp-result', 'error');
            }
        }
        
        async function testMessageProcessing() {
            log('⚙️ Probando procesamiento de mensajes...', 'whatsapp-result');
            try {
                const token = await getAuthToken();
                const response = await fetch('http://localhost:3001/api/whatsapp/conversaciones', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (response.ok) {
                    const conversations = await response.json();
                    log(`✅ Procesamiento de mensajes activo - ${conversations.length} conversaciones`, 'whatsapp-result', 'success');
                } else {
                    log('❌ Error en procesamiento de mensajes', 'whatsapp-result', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'whatsapp-result', 'error');
            }
        }
        
        async function testAutomatedResponse() {
            log('🤖 Probando respuesta automática...', 'whatsapp-result');
            try {
                const token = await getAuthToken();
                const response = await fetch('http://localhost:3001/api/waapi/send-message', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        phone_number: '+50312345678',
                        message: 'Mensaje de prueba automático desde el sistema'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ Sistema de respuesta automática funcionando', 'whatsapp-result', 'success');
                } else {
                    log('❌ Error en respuesta automática', 'whatsapp-result', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'whatsapp-result', 'error');
            }
        }
        
        async function checkN8nConfig() {
            log('⚙️ Verificando configuración de n8n...', 'config-result');
            try {
                const token = await getAuthToken();
                const response = await fetch('http://localhost:3001/api/waapi/config', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (response.ok) {
                    const config = await response.json();
                    log('✅ Configuración de n8n obtenida:', 'config-result', 'success');
                    log(`   - URL n8n: ${config.n8n_url}`, 'config-result', 'info');
                    log(`   - API Key configurada: ${config.n8n_api_key ? 'Sí' : 'No'}`, 'config-result', 'info');
                    log(`   - Auto-reply: ${config.auto_reply ? 'Activado' : 'Desactivado'}`, 'config-result', 'info');
                } else {
                    log('❌ Error obteniendo configuración', 'config-result', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'config-result', 'error');
            }
        }
        
        async function testN8nAPI() {
            log('🔑 Probando API de n8n...', 'config-result');
            try {
                // Intentar acceder a la API de n8n con autenticación básica
                const credentials = btoa('admin:K@rur0su24');
                const response = await fetch('http://localhost:5678/api/v1/workflows', {
                    headers: { 'Authorization': `Basic ${credentials}` }
                });
                
                if (response.ok) {
                    const workflows = await response.json();
                    log(`✅ API de n8n accesible - ${workflows.data ? workflows.data.length : 0} workflows`, 'config-result', 'success');
                } else {
                    log(`⚠️ API de n8n responde pero con error: ${response.status}`, 'config-result', 'error');
                }
            } catch (error) {
                log(`❌ Error accediendo a API de n8n: ${error.message}`, 'config-result', 'error');
            }
        }
        
        async function importWorkflow() {
            log('📥 Importando flujo de trabajo de WhatsApp...', 'config-result');
            log('ℹ️ Esta funcionalidad requiere configuración manual en n8n', 'config-result', 'info');
            log('📋 Pasos para importar:', 'config-result', 'info');
            log('   1. Abrir n8n en http://localhost:5678', 'config-result', 'info');
            log('   2. Crear nuevo workflow', 'config-result', 'info');
            log('   3. Importar desde n8n-whatsapp-flow.json', 'config-result', 'info');
            log('   4. Configurar credenciales y webhooks', 'config-result', 'info');
        }
        
        async function getAuthToken() {
            const response = await fetch('http://localhost:3001/api/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'admin123'
                })
            });
            
            if (response.ok) {
                const data = await response.json();
                return data.token;
            }
            throw new Error('No se pudo obtener token de autenticación');
        }
        
        // Auto-test al cargar la página
        window.onload = function() {
            log('🚀 Iniciando verificación de comunicación n8n...', 'services-result');
        };
    </script>
</body>
</html>
