const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🤖 Creando workflow con OpenAI + Memoria manual...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow con OpenAI y memoria manual
async function createOpenAIWithMemoryWorkflow() {
  console.log('\n🧠 Creando workflow con OpenAI y memoria manual...');
  
  const workflowData = {
    name: 'WhatsApp OpenAI - Con Memoria y Audio',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir mensajes
      {
        parameters: {
          httpMethod: 'POST',
          path: 'openai-memory',
          responseMode: 'responseNode'
        },
        id: 'webhook-openai',
        name: 'WhatsApp OpenAI Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Verificar si es audio o texto
      {
        parameters: {
          conditions: {
            options: {
              caseSensitive: true,
              leftValue: '',
              typeValidation: 'strict'
            },
            conditions: [
              {
                id: 'audio-check',
                leftValue: '={{ $json.type }}',
                rightValue: 'audio',
                operator: {
                  type: 'string',
                  operation: 'equals'
                }
              }
            ],
            combinator: 'and'
          },
          options: {}
        },
        id: 'check-audio',
        name: 'Es Audio?',
        type: 'n8n-nodes-base.if',
        typeVersion: 2,
        position: [460, 300]
      },
      
      // 3. Transcribir audio (rama TRUE)
      {
        parameters: {
          resource: 'audio',
          operation: 'transcribe',
          file: '={{ $json.audio_url }}',
          options: {
            language: 'es',
            prompt: 'Transcripción de mensaje de WhatsApp del Colegio Cristiano Jerusalén. Puede contener reportes de ausencias, consultas o reportes de comportamiento.'
          }
        },
        id: 'transcribe-audio',
        name: 'Transcribir con Whisper',
        type: 'n8n-nodes-base.openAi',
        typeVersion: 1,
        position: [680, 200],
        credentials: {
          openAiApi: {
            id: 'openai-ccjap',
            name: 'OpenAI CCJAP'
          }
        }
      },
      
      // 4. Preparar texto transcrito
      {
        parameters: {
          jsCode: `
const originalData = $('WhatsApp OpenAI Webhook').first().json;
const transcription = $input.first().json.text;

return [{
  json: {
    from: originalData.from,
    name: originalData.name,
    body: transcription,
    type: 'audio_transcribed',
    original_audio_url: originalData.audio_url
  }
}];
`
        },
        id: 'prepare-transcribed',
        name: 'Preparar Transcripción',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 200]
      },
      
      // 5. Preparar texto directo (rama FALSE)
      {
        parameters: {
          jsCode: `
const data = $input.first().json;
return [{
  json: {
    from: data.from,
    name: data.name,
    body: data.body,
    type: 'text'
  }
}];
`
        },
        id: 'prepare-text',
        name: 'Preparar Texto',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [680, 400]
      },
      
      // 6. Unir ambas ramas
      {
        parameters: {},
        id: 'merge-messages',
        name: 'Unir Mensajes',
        type: 'n8n-nodes-base.merge',
        typeVersion: 3,
        position: [1120, 300]
      },
      
      // 7. Obtener historial de conversación (MEMORIA)
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.from }}'
            }
          },
          table: 'mensajes_whatsapp',
          limit: 5,
          sort: {
            values: [
              {
                column: 'fecha_recepcion',
                direction: 'DESC'
              }
            ]
          }
        },
        id: 'get-memory',
        name: 'Obtener Memoria',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1340, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 8. Preparar contexto completo para OpenAI
      {
        parameters: {
          jsCode: `
// Preparar contexto completo con memoria para OpenAI
const currentMessage = $('Unir Mensajes').first().json;
const historyData = $input.all();

// Construir historial de conversación
let conversationHistory = '';
if (historyData.length > 0) {
  conversationHistory = '\\nHISTORIAL DE CONVERSACIÓN RECIENTE:\\n';
  historyData.slice(0, 3).forEach((msg, index) => {
    const fecha = new Date(msg.json.fecha_recepcion).toLocaleDateString();
    const mensaje = msg.json.texto_mensaje || 'Sin mensaje';
    const respuesta = msg.json.response_message || 'Sin respuesta';
    
    conversationHistory += \`\${index + 1}. [\${fecha}] Padre: "\${mensaje}"\\n\`;
    if (respuesta !== 'Sin respuesta') {
      conversationHistory += \`   Asistente: "\${respuesta}"\\n\`;
    }
  });
  conversationHistory += '\\n';
}

// Sistema prompt especializado
const systemPrompt = \`Eres el asistente inteligente del Colegio Cristiano Jerusalén de los Altos de Palencia (CCJAP).

INFORMACIÓN DEL COLEGIO:
- Nombre: Colegio Cristiano Jerusalén de los Altos de Palencia
- Horario de clases: 7:00 AM - 12:00 PM (Lunes a Viernes)
- Horario de oficina: 7:00 AM - 3:00 PM (Lunes a Viernes)
- Teléfono: +503 1234-5678
- Ubicación: Palencia, El Salvador

CAPACIDADES:
1. AUSENCIAS: Procesar reportes de inasistencia estudiantil
2. CONSULTAS: Responder sobre horarios, actividades, información general
3. REPORTES: Gestionar reportes de comportamiento
4. COMUNICACIÓN: Facilitar comunicación entre padres y colegio

INSTRUCCIONES:
- Responde de forma profesional, empática y útil
- Para ausencias: confirma recepción y notifica que se informará al docente
- Para consultas: proporciona información precisa del colegio
- Para reportes: confirma recepción y explica el proceso
- Usa emojis apropiados
- Recuerda conversaciones anteriores y haz referencia a ellas
- Mantén un tono cálido pero profesional

\${conversationHistory}

MENSAJE ACTUAL:
Remitente: \${currentMessage.name} (\${currentMessage.from})
Tipo: \${currentMessage.type === 'audio_transcribed' ? '🎤 Audio transcrito' : '💬 Texto'}
Mensaje: "\${currentMessage.body}"

Analiza el mensaje, recuerda el contexto de conversaciones anteriores, y proporciona una respuesta apropiada.\`;

return [{
  json: {
    system_prompt: systemPrompt,
    user_message: currentMessage.body,
    sender_info: {
      phone: currentMessage.from,
      name: currentMessage.name,
      message_type: currentMessage.type
    },
    has_history: historyData.length > 0
  }
}];
`
        },
        id: 'prepare-openai-context',
        name: 'Preparar Contexto OpenAI',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1560, 300]
      },
      
      // 9. OpenAI Chat Completion con memoria
      {
        parameters: {
          model: 'gpt-4',
          messages: {
            values: [
              {
                role: 'system',
                content: '={{ $json.system_prompt }}'
              },
              {
                role: 'user',
                content: '={{ $json.user_message }}'
              }
            ]
          },
          options: {
            temperature: 0.7,
            maxTokens: 800,
            topP: 1,
            frequencyPenalty: 0,
            presencePenalty: 0
          }
        },
        id: 'openai-chat',
        name: 'OpenAI Chat con Memoria',
        type: 'n8n-nodes-base.openAi',
        typeVersion: 1,
        position: [1780, 300],
        credentials: {
          openAiApi: {
            id: 'openai-ccjap',
            name: 'OpenAI CCJAP'
          }
        }
      },
      
      // 10. Procesar respuesta de OpenAI
      {
        parameters: {
          jsCode: `
// Procesar respuesta de OpenAI
const openaiResponse = $input.first().json.message.content;
const contextData = $('Preparar Contexto OpenAI').first().json;

// Análisis del tipo de mensaje
const mensaje = contextData.user_message.toLowerCase();
let tipoMensaje = 'general';
let confianza = 0.8;
let requiereAprobacion = false;

if (mensaje.includes('ausencia') || mensaje.includes('no asistirá') || mensaje.includes('enfermo')) {
  tipoMensaje = 'ausencia';
  confianza = 0.95;
  requiereAprobacion = false;
} else if (mensaje.includes('horario') || mensaje.includes('información') || mensaje.includes('pregunta')) {
  tipoMensaje = 'consulta';
  confianza = 0.9;
  requiereAprobacion = false;
} else if (mensaje.includes('reporte') || mensaje.includes('problema') || mensaje.includes('comportamiento')) {
  tipoMensaje = 'reporte_comportamiento';
  confianza = 0.9;
  requiereAprobacion = true;
} else if (mensaje.includes('urgente') || mensaje.includes('director')) {
  tipoMensaje = 'director_atencion';
  confianza = 0.85;
  requiereAprobacion = true;
}

const analisisCompleto = {
  mensaje_original: contextData.user_message,
  respuesta_openai: openaiResponse,
  clasificacion: {
    tipo_mensaje: tipoMensaje,
    confianza: confianza,
    requiere_aprobacion: requiereAprobacion
  },
  remitente: contextData.sender_info,
  memoria_utilizada: contextData.has_history,
  timestamp: new Date().toISOString(),
  procesado_por: 'openai-gpt4-con-memoria'
};

console.log('🤖 Análisis OpenAI con memoria completado:', JSON.stringify(analisisCompleto, null, 2));

return [{
  json: analisisCompleto
}];
`
        },
        id: 'process-openai-response',
        name: 'Procesar Respuesta OpenAI',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [2000, 300]
      },
      
      // 11. Guardar en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.remitente.phone }}',
              nombre_remitente: '={{ $json.remitente.name }}',
              texto_mensaje: '={{ $json.mensaje_original }}',
              tipo_mensaje: '={{ $json.clasificacion.tipo_mensaje }}',
              ai_analysis: '={{ JSON.stringify($json) }}',
              confidence_score: '={{ $json.clasificacion.confianza }}',
              requires_approval: '={{ $json.clasificacion.requiere_aprobacion }}',
              response_message: '={{ $json.respuesta_openai }}',
              procesado: true,
              fecha_recepcion: '={{ new Date().toISOString() }}'
            }
          },
          table: 'mensajes_whatsapp'
        },
        id: 'save-with-memory',
        name: 'Guardar con Memoria',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [2220, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 12. Respuesta final
      {
        parameters: {
          options: {}
        },
        id: 'webhook-response',
        name: 'Respuesta Final',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [2440, 300]
      }
    ],
    
    connections: {
      'WhatsApp OpenAI Webhook': {
        main: [
          [
            {
              node: 'Es Audio?',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Es Audio?': {
        main: [
          [
            {
              node: 'Transcribir con Whisper',
              type: 'main',
              index: 0
            }
          ],
          [
            {
              node: 'Preparar Texto',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Transcribir con Whisper': {
        main: [
          [
            {
              node: 'Preparar Transcripción',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Transcripción': {
        main: [
          [
            {
              node: 'Unir Mensajes',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Texto': {
        main: [
          [
            {
              node: 'Unir Mensajes',
              type: 'main',
              index: 1
            }
          ]
        ]
      },
      'Unir Mensajes': {
        main: [
          [
            {
              node: 'Obtener Memoria',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Obtener Memoria': {
        main: [
          [
            {
              node: 'Preparar Contexto OpenAI',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Contexto OpenAI': {
        main: [
          [
            {
              node: 'OpenAI Chat con Memoria',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'OpenAI Chat con Memoria': {
        main: [
          [
            {
              node: 'Procesar Respuesta OpenAI',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar Respuesta OpenAI': {
        main: [
          [
            {
              node: 'Guardar con Memoria',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Guardar con Memoria': {
        main: [
          [
            {
              node: 'Respuesta Final',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow con OpenAI y memoria creado con ID: ${result.id}`);
    
    // Activar el workflow
    await makeN8nRequest('POST', `/workflows/${result.id}/activate`);
    console.log(`✅ Workflow activado automáticamente`);
    
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow con OpenAI:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow con OpenAI y memoria
    const openaiWorkflowId = await createOpenAIWithMemoryWorkflow();
    
    console.log('\n🎉 ¡Workflow con OpenAI y memoria creado exitosamente!');
    console.log('📋 Características implementadas:');
    console.log('   ✅ OpenAI GPT-4 real');
    console.log('   ✅ Memoria manual de conversaciones');
    console.log('   ✅ Transcripción de audio con Whisper');
    console.log('   ✅ Análisis inteligente de mensajes');
    console.log('   ✅ Clasificación automática por tipo');
    console.log('   ✅ Almacenamiento en base de datos');
    
    console.log('\n🔗 URL de Webhook:');
    console.log(`   - OpenAI con Memoria: ${N8N_BASE_URL}/webhook/openai-memory`);
    
    console.log('\n🧪 Ejemplos de prueba:');
    console.log('\n📝 Mensaje de texto:');
    console.log('curl -X POST http://localhost:5678/webhook/openai-memory \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"from": "50312345678", "body": "Mi hijo Juan no asistirá hoy", "name": "María", "type": "text"}\'');
    
    console.log('\n🎤 Mensaje de audio:');
    console.log('curl -X POST http://localhost:5678/webhook/openai-memory \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"from": "50312345678", "audio_url": "https://example.com/audio.mp3", "name": "María", "type": "audio"}\'');
    
    console.log('\n⚠️ IMPORTANTE:');
    console.log('1. Configurar credencial OpenAI en n8n: "OpenAI CCJAP"');
    console.log('2. Configurar credencial PostgreSQL en n8n: "PostgreSQL Local"');
    console.log('3. El workflow mantendrá memoria por número de teléfono');
    console.log('4. Soporta tanto texto como audio con transcripción');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
