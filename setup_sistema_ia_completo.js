#!/usr/bin/env node

/**
 * Script de Configuración Completa del Sistema de IA WhatsApp
 * Colegio Cristiano Jerusalén de los Altos de Palencia
 * 
 * Este script configura todo el sistema de IA para WhatsApp incluyendo:
 * - Workflows de n8n con ChatGPT
 * - Base de datos con tablas necesarias
 * - Credenciales y configuraciones
 * - Sistema de notificaciones y aprobaciones
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log(`
╔══════════════════════════════════════════════════════════════════════════════╗
║                    SISTEMA DE IA WHATSAPP - CCJAP                           ║
║                     Configuración Automática Completa                       ║
╚══════════════════════════════════════════════════════════════════════════════╝
`);

async function verificarPrerequisitos() {
  console.log("🔍 Verificando prerrequisitos...");
  
  const requisitos = [
    { comando: 'docker --version', nombre: 'Docker' },
    { comando: 'docker-compose --version', nombre: 'Docker Compose' },
    { comando: 'node --version', nombre: 'Node.js' }
  ];
  
  for (const req of requisitos) {
    try {
      execSync(req.comando, { stdio: 'ignore' });
      console.log(`✅ ${req.nombre} instalado`);
    } catch (error) {
      console.log(`❌ ${req.nombre} NO encontrado`);
      console.log(`   Por favor instale ${req.nombre} antes de continuar`);
      process.exit(1);
    }
  }
}

async function verificarVariablesEntorno() {
  console.log("\n🔧 Verificando variables de entorno...");
  
  const variables = [
    'CHATGPT_API_KEY',
    'N8N_API_KEY'
  ];
  
  const faltantes = [];
  
  for (const variable of variables) {
    if (!process.env[variable]) {
      faltantes.push(variable);
    } else {
      console.log(`✅ ${variable} configurada`);
    }
  }
  
  if (faltantes.length > 0) {
    console.log(`\n⚠️  Variables de entorno faltantes:`);
    faltantes.forEach(v => console.log(`   - ${v}`));
    console.log(`\n📝 Cree un archivo .env con las siguientes variables:`);
    console.log(`CHATGPT_API_KEY=sk-your-openai-api-key-here`);
    console.log(`N8N_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`);
    console.log(`\n💡 Puede obtener la API key de ChatGPT en: https://platform.openai.com/api-keys`);
    
    // Crear archivo .env de ejemplo
    const envExample = `# Variables de entorno para Sistema IA WhatsApp
CHATGPT_API_KEY=sk-your-openai-api-key-here
N8N_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU
WAAPI_TOKEN=your-waapi-token-here
N8N_URL=http://localhost:5678
BACKEND_URL=http://backend:3001
`;
    
    fs.writeFileSync('.env.example', envExample);
    console.log(`\n📄 Archivo .env.example creado como referencia`);
    
    return false;
  }
  
  return true;
}

async function iniciarServicios() {
  console.log("\n🚀 Iniciando servicios Docker...");
  
  try {
    console.log("   Deteniendo servicios existentes...");
    execSync('docker-compose down', { stdio: 'inherit' });
    
    console.log("   Iniciando servicios...");
    execSync('docker-compose up -d', { stdio: 'inherit' });
    
    console.log("   Esperando que los servicios estén listos...");
    await new Promise(resolve => setTimeout(resolve, 30000)); // Esperar 30 segundos
    
    console.log("✅ Servicios Docker iniciados");
  } catch (error) {
    console.error("❌ Error iniciando servicios Docker:", error.message);
    throw error;
  }
}

async function configurarBaseDatos() {
  console.log("\n🗄️  Configurando base de datos...");
  
  try {
    // Ejecutar script de creación de tablas
    console.log("   Creando tablas de IA...");
    execSync('node backend/scripts/setup_complete_ai_system.js', { stdio: 'inherit' });
    
    console.log("✅ Base de datos configurada");
  } catch (error) {
    console.error("❌ Error configurando base de datos:", error.message);
    throw error;
  }
}

async function mostrarResumen() {
  console.log(`
╔══════════════════════════════════════════════════════════════════════════════╗
║                           ¡CONFIGURACIÓN COMPLETADA!                        ║
╚══════════════════════════════════════════════════════════════════════════════╝

🎉 El Sistema de IA WhatsApp ha sido configurado exitosamente.

📋 SERVICIOS DISPONIBLES (DESARROLLO LOCAL):
   • Frontend:     http://localhost:5173
   • Backend API:  http://localhost:3001
   • n8n:          http://localhost:5678
   • PostgreSQL:   localhost:5432

🔗 WEBHOOKS CONFIGURADOS (LOCAL):
   • Principal:           http://localhost:5678/webhook/whatsapp-main
   • Ausencias:          http://localhost:5678/webhook/process-absence
   • Reportes:           http://localhost:5678/webhook/behavior-report
   • Reportes Aprobados: http://localhost:5678/webhook/send-approved-report

🤖 WORKFLOWS CREADOS:
   ✅ Análisis Principal con ChatGPT
   ✅ Gestión de Ausencias Automáticas
   ✅ Reportes de Comportamiento
   ✅ Sistema de Aprobaciones del Director
   ✅ Reportes Diarios Automáticos (8:00 AM)

📱 FUNCIONALIDADES ACTIVAS:
   ✅ Análisis inteligente de mensajes WhatsApp
   ✅ Detección automática de ausencias
   ✅ Reportes con evidencia multimedia
   ✅ Sistema de aprobación del director
   ✅ Notificaciones en tiempo real
   ✅ Envío automático de reportes diarios

🔧 PRÓXIMOS PASOS (DESARROLLO LOCAL):
   1. Acceder a n8n: http://localhost:5678
      Usuario: admin / Contraseña: K@rur0su24

   2. Verificar que todos los workflows estén activos

   3. Configurar webhook en WaAPI (para pruebas locales):
      URL: http://localhost:5678/webhook/whatsapp-main
      (Nota: Para pruebas locales usar ngrok o similar para exponer el puerto)

   4. Probar el sistema enviando un mensaje de prueba

📖 DOCUMENTACIÓN:
   Ver archivo: SISTEMA_IA_WHATSAPP.md

🆘 SOPORTE:
   Email: <EMAIL>
   Teléfono: +503 1234-5678

╔══════════════════════════════════════════════════════════════════════════════╗
║  ¡El sistema está listo para recibir y procesar mensajes de WhatsApp!       ║
╚══════════════════════════════════════════════════════════════════════════════╝
`);
}

async function main() {
  try {
    await verificarPrerequisitos();
    
    const envOk = await verificarVariablesEntorno();
    if (!envOk) {
      console.log("\n⏸️  Configuración pausada. Configure las variables de entorno y ejecute nuevamente.");
      process.exit(1);
    }
    
    await iniciarServicios();
    await configurarBaseDatos();
    await mostrarResumen();
    
    console.log("\n🎊 ¡Configuración completada exitosamente!");
    
  } catch (error) {
    console.error("\n💥 Error durante la configuración:", error.message);
    console.log("\n🔧 Pasos para solucionar:");
    console.log("1. Verificar que Docker esté ejecutándose");
    console.log("2. Verificar las variables de entorno");
    console.log("3. Verificar conectividad a internet");
    console.log("4. Revisar logs de Docker: docker-compose logs");
    
    process.exit(1);
  }
}

// Ejecutar configuración
if (require.main === module) {
  main();
}

module.exports = { main };
