{"name": "Procesamiento de Mensajes de WhatsApp", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"httpMethod": "POST", "path": "whatsapp-webhook", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300], "webhookId": "whatsapp-webhook"}, {"parameters": {"functionCode": "// Procesar mensaje de WhatsApp - Sistema Avanzado CCJAP\nconst message = $input.first();\n\n// Extraer información del mensaje\nconst body = String(message.json.body || message.json.text || '').toLowerCase();\nconst originalBody = String(message.json.body || message.json.text || '');\nconst from = String(message.json.from || '');\nconst timestamp = message.json.timestamp || new Date().toISOString();\n\n// Variables de procesamiento\nlet messageType = 'OTRO';\nlet responseMessage = '';\nlet isAbsence = false;\nlet isUrgent = false;\nlet studentName = '';\nlet grade = '';\nlet section = '';\nlet reason = '';\nlet priority = 'normal';\nlet requiresAttention = false;\nlet suggestedActions = [];\n\nif (body && typeof body === 'string') {\n  // 1. REPORTES DE AUSENCIA (Patrones múltiples)\n  const absencePatterns = [\n    /(?:ausente|no\\s*asist|falta|no\\s*va|no\\s*viene)[\\s:]*([^,\\n]+)(?:[,\\s]*(?:grado|°)?\\s*(\\d+)\\s*([a-z]?))?/i,\n    /([^,\\n]+)\\s*(?:no\\s*asist|falta|ausente|enferm|doctor|cita)/i,\n    /mi\\s*hij[oa]\\s*([^,\\n]+)\\s*(?:no\\s*podr[áa]|falta|ausente)/i,\n    /reporte\\s*ausencia[\\s:]*([^,\\n]+)/i\n  ];\n  \n  for (let pattern of absencePatterns) {\n    const match = originalBody.match(pattern);\n    if (match) {\n      messageType = 'AUSENCIA';\n      isAbsence = true;\n      studentName = (match[1] || '').trim();\n      grade = match[2] || '';\n      section = (match[3] || 'A').toUpperCase();\n      \n      // Detectar razón de ausencia\n      if (/enferm|doctor|m[eé]dic|hospital|cita/i.test(body)) {\n        reason = 'médica';\n        priority = 'normal';\n      } else if (/emergencia|urgente|accidente/i.test(body)) {\n        reason = 'emergencia';\n        priority = 'alta';\n        isUrgent = true;\n      } else if (/viaje|familiar|personal/i.test(body)) {\n        reason = 'personal';\n        priority = 'normal';\n      } else {\n        reason = 'no especificada';\n      }\n      \n      responseMessage = `✅ Ausencia registrada: ${studentName}${grade ? ` (${grade}°${section})` : ''}\\n📋 Motivo: ${reason}\\n⏰ Fecha: ${new Date().toLocaleDateString()}\\n\\n📞 Si necesita justificante médico, favor enviarlo posteriormente.`;\n      suggestedActions = ['generar_reporte', 'notificar_docente', 'actualizar_asistencia'];\n      break;\n    }\n  }\n  \n  // 2. CONSULTAS DE CALIFICACIONES (Patrones avanzados)\n  if (!isAbsence && (/calificaci|nota|punt|examen|evaluaci|resultado/i.test(body))) {\n    messageType = 'CONSULTA_CALIFICACIONES';\n    \n    // Detectar materia específica\n    let subject = '';\n    if (/matem[áa]tica|mate/i.test(body)) subject = 'Matemáticas';\n    else if (/espa[ñn]ol|lenguaje/i.test(body)) subject = 'Español';\n    else if (/ciencia|naturales/i.test(body)) subject = 'Ciencias';\n    else if (/social|estudios/i.test(body)) subject = 'Estudios Sociales';\n    else if (/ingl[eé]s/i.test(body)) subject = 'Inglés';\n    \n    responseMessage = `📊 Consulta de calificaciones recibida\\n\\n` +\n      `Para consultar las notas${subject ? ` de ${subject}` : ''}, necesito:\\n` +\n      `👤 Nombre completo del estudiante\\n` +\n      `🎓 Grado y sección\\n` +\n      `📅 Período a consultar\\n\\n` +\n      `📱 Las calificaciones se enviarán por este medio en las próximas horas.`;\n    \n    requiresAttention = true;\n    suggestedActions = ['verificar_estudiante', 'consultar_notas', 'generar_reporte'];\n  }\n  \n  // 3. CONSULTAS DE TAREAS Y DEBERES\n  else if (!isAbsence && (/tarea|deber|asignaci[óo]n|trabajo|proyecto/i.test(body))) {\n    messageType = 'CONSULTA_TAREAS';\n    \n    responseMessage = `📚 Consulta de tareas recibida\\n\\n` +\n      `Las tareas del día se encuentran en:\\n` +\n      `📖 Cuaderno de comunicaciones\\n` +\n      `💻 Plataforma educativa\\n` +\n      `📱 Grupo de WhatsApp del grado\\n\\n` +\n      `⏰ Horario de consultas: 2:00 PM - 4:00 PM`;\n    \n    suggestedActions = ['enviar_tareas', 'notificar_docente'];\n  }\n  \n  // 4. CONSULTAS DE HORARIOS\n  else if (!isAbsence && (/horario|hora|entrada|salida|recreo/i.test(body))) {\n    messageType = 'HORARIO';\n    \n    responseMessage = `🕐 Información de horarios CCJAP\\n\\n` +\n      `📅 Lunes a Viernes:\\n` +\n      `🌅 Entrada: 7:00 AM\\n` +\n      `🌆 Salida: 3:00 PM\\n` +\n      `☕ Recreo: 9:30 AM - 9:45 AM\\n` +\n      `🍽️ Almuerzo: 12:00 PM - 12:30 PM\\n\\n` +\n      `📞 Oficina: 7:00 AM - 4:00 PM`;\n  }\n  \n  // 5. CONSULTAS SOBRE EVENTOS\n  else if (!isAbsence && (/evento|actividad|reuni[óo]n|festival|acto/i.test(body))) {\n    messageType = 'CONSULTA_EVENTOS';\n    \n    responseMessage = `🎉 Próximos eventos CCJAP\\n\\n` +\n      `📅 Esta semana:\\n` +\n      `• Reunión de padres: Viernes 3:00 PM\\n` +\n      `• Festival de talentos: Próximo mes\\n\\n` +\n      `📢 Manténgase atento a las circulares informativas.`;\n    \n    suggestedActions = ['enviar_calendario', 'notificar_eventos'];\n  }\n  \n  // 6. CONSULTAS ADMINISTRATIVAS\n  else if (!isAbsence && (/pago|mensualidad|beca|matricula|documento|certificado/i.test(body))) {\n    messageType = 'CONSULTA_ADMINISTRATIVA';\n    \n    responseMessage = `🏢 Consulta administrativa recibida\\n\\n` +\n      `📋 Para trámites administrativos:\\n` +\n      `🕐 Horario: 7:00 AM - 3:00 PM\\n` +\n      `📍 Oficina de administración\\n` +\n      `📞 Teléfono: 2XXX-XXXX\\n\\n` +\n      `📄 Documentos necesarios: DUI, partida de nacimiento del estudiante.`;\n    \n    requiresAttention = true;\n    priority = 'alta';\n    suggestedActions = ['derivar_administracion', 'programar_cita'];\n  }\n  \n  // 7. EMERGENCIAS Y URGENCIAS\n  else if (/emergencia|urgente|accidente|hospital|ambulancia/i.test(body)) {\n    messageType = 'EMERGENCIA';\n    isUrgent = true;\n    priority = 'crítica';\n    requiresAttention = true;\n    \n    responseMessage = `🚨 EMERGENCIA DETECTADA\\n\\n` +\n      `Su mensaje ha sido marcado como URGENTE\\n` +\n      `📞 Contacto inmediato: 2XXX-XXXX\\n` +\n      `🏥 En caso de emergencia médica: 911\\n\\n` +\n      `⚡ Un miembro del personal se comunicará inmediatamente.`;\n    \n    suggestedActions = ['notificar_director', 'llamada_inmediata', 'registrar_emergencia'];\n  }\n  \n  // 8. SALUDOS Y CORTESÍAS\n  else if (/hola|buen|saludo|gracias|thank/i.test(body)) {\n    messageType = 'SALUDO';\n    \n    const hour = new Date().getHours();\n    let greeting = '¡Hola!';\n    if (hour < 12) greeting = '¡Buenos días!';\n    else if (hour < 18) greeting = '¡Buenas tardes!';\n    else greeting = '¡Buenas noches!';\n    \n    responseMessage = `${greeting} 👋\\n\\n` +\n      `Bienvenido al sistema automatizado del CCJAP\\n\\n` +\n      `Puedo ayudarte con:\\n` +\n      `📝 Reportar ausencias\\n` +\n      `📊 Consultar calificaciones\\n` +\n      `📚 Información de tareas\\n` +\n      `🕐 Horarios y eventos\\n` +\n      `🏢 Trámites administrativos\\n\\n` +\n      `¿En qué puedo asistirte hoy?`;\n  }\n}\n\n// Preparar datos estructurados para el backend\nconst payload = {\n  from: from,\n  text: originalBody,\n  messageType: messageType,\n  studentName: studentName,\n  grade: grade,\n  section: section,\n  reason: reason,\n  isAbsence: isAbsence,\n  isUrgent: isUrgent,\n  priority: priority,\n  requiresAttention: requiresAttention,\n  suggestedActions: suggestedActions,\n  timestamp: timestamp,\n  processedAt: new Date().toISOString()\n};\n\n// Agregar respuesta automática si existe\nif (responseMessage) {\n  payload.response = {\n    message: responseMessage,\n    type: 'AUTOMATIC_REPLY',\n    priority: priority,\n    requiresFollowUp: requiresAttention\n  };\n}\n\nreturn {\n  json: payload\n};"}, "name": "<PERSON>ces<PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [700, 300]}, {"parameters": {"requestMethod": "POST", "url": "http://backend:3001/api/webhook/whatsapp", "options": {}}, "name": "Enviar a Backend", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [950, 300]}, {"parameters": {"functionCode": "// Generar reporte automático de ausencias\nconst data = $input.first().json;\n\nif (data.isAbsence) {\n  const reportData = {\n    tipo: 'reporte_ausencia',\n    estudiante: data.studentName,\n    grado: data.grade,\n    seccion: data.section,\n    motivo: data.reason,\n    fecha: new Date().toISOString().split('T')[0],\n    hora: new Date().toLocaleTimeString(),\n    telefono_padre: data.from,\n    prioridad: data.priority,\n    requiere_seguimiento: data.requiresAttention,\n    acciones_sugeridas: data.suggestedActions\n  };\n  \n  return {\n    json: reportData\n  };\n}\n\nreturn {\n  json: { skip: true }\n};"}, "name": "Generar Reporte Ausencia", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [950, 150]}, {"parameters": {"requestMethod": "POST", "url": "http://backend:3001/api/reportes/ausencias", "options": {"headers": {"Content-Type": "application/json"}}}, "name": "Guardar Reporte", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1200, 150]}, {"parameters": {"functionCode": "// Determinar notificaciones necesarias\nconst data = $input.first().json;\nconst notifications = [];\n\n// Notificación a director para emergencias\nif (data.isUrgent || data.priority === 'crítica') {\n  notifications.push({\n    tipo: 'director',\n    mensaje: `🚨 URGENTE: ${data.messageType} - ${data.studentName || 'Consulta importante'}`,\n    telefono: data.from,\n    prioridad: 'alta'\n  });\n}\n\n// Notificación a docente para ausencias\nif (data.isAbsence && data.grade) {\n  notifications.push({\n    tipo: 'docente',\n    mensaje: `📝 Ausencia reportada: ${data.studentName} - ${data.grade}°${data.section}`,\n    grado: data.grade,\n    seccion: data.section,\n    motivo: data.reason\n  });\n}\n\n// Notificación a administración\nif (data.messageType === 'CONSULTA_ADMINISTRATIVA') {\n  notifications.push({\n    tipo: 'administracion',\n    mensaje: `🏢 Consulta administrativa de ${data.from}`,\n    requiere_atencion: true\n  });\n}\n\nreturn notifications.map(notif => ({ json: notif }));"}, "name": "Procesar Notificaciones", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [950, 450]}, {"parameters": {"requestMethod": "POST", "url": "http://backend:3001/api/notificaciones/enviar", "options": {"headers": {"Content-Type": "application/json"}}}, "name": "Enviar Notificaciones", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1200, 450]}, {"parameters": {"functionCode": "// Generar respuesta automática mejorada\nconst data = $input.first().json;\n\nif (data.response && data.response.message) {\n  let responseText = data.response.message;\n  \n  // Agregar información contextual\n  if (data.isAbsence) {\n    responseText += `\\n\\n📋 Número de reporte: AUS-${Date.now().toString().slice(-6)}`;\n    responseText += `\\n⏰ Registrado: ${new Date().toLocaleString()}`;\n  }\n  \n  if (data.isUrgent) {\n    responseText = `🚨 URGENTE 🚨\\n\\n${responseText}`;\n  }\n  \n  // Agregar firma institucional\n  responseText += `\\n\\n---\\n🏫 Colegio Cristiano Japonés\\n📱 Sistema Automatizado`;\n  \n  return {\n    json: {\n      message: responseText,\n      priority: data.priority || 'normal',\n      timestamp: new Date().toISOString()\n    }\n  };\n}\n\nreturn {\n  json: {\n    message: '✅ Mensaje recibido y procesado correctamente.',\n    timestamp: new Date().toISOString()\n  }\n};"}, "name": "Generar Respuesta Mejo<PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1200, 300]}, {"parameters": {"options": {}}, "name": "Respuesta Final", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1450, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>ces<PERSON>", "type": "main", "index": 0}]]}, "Procesar Mensaje": {"main": [[{"node": "Enviar a Backend", "type": "main", "index": 0}, {"node": "Generar Reporte Ausencia", "type": "main", "index": 0}, {"node": "Procesar Notificaciones", "type": "main", "index": 0}]]}, "Enviar a Backend": {"main": [[{"node": "Generar Respuesta Mejo<PERSON>", "type": "main", "index": 0}]]}, "Generar Reporte Ausencia": {"main": [[{"node": "Guardar Reporte", "type": "main", "index": 0}]]}, "Procesar Notificaciones": {"main": [[{"node": "Enviar Notificaciones", "type": "main", "index": 0}]]}, "Generar Respuesta Mejorada": {"main": [[{"node": "Respuesta Final", "type": "main", "index": 0}]]}}, "settings": {}}