const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🚀 Creando workflows de IA en n8n...');
console.log(`📍 n8n URL: ${N8N_BASE_URL}`);

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow principal con IA
async function createMainWorkflow() {
  console.log('\n📋 Creando workflow principal de análisis con IA...');
  
  const workflowData = {
    name: 'WhatsApp IA - Análisis Principal',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir mensajes de WhatsApp
      {
        parameters: {
          httpMethod: 'POST',
          path: 'whatsapp-main',
          responseMode: 'responseNode'
        },
        id: 'webhook-main',
        name: 'WhatsApp Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Guardar mensaje en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.from }}',
              texto_mensaje: '={{ $json.body }}',
              fecha_recepcion: '={{ new Date().toISOString() }}',
              procesado: false,
              nombre_remitente: '={{ $json.name || $json.from }}',
              tipo_mensaje: 'pendiente'
            }
          },
          table: 'mensajes_whatsapp'
        },
        id: 'save-message',
        name: 'Guardar Mensaje',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 3. Análisis con IA (simulado por ahora)
      {
        parameters: {
          jsCode: `
// Análisis básico del mensaje
const mensaje = $input.first().json.body || '';
const telefono = $input.first().json.from || '';
const nombre = $input.first().json.name || telefono;

// Palabras clave para detectar tipos de mensaje
const palabrasAusencia = ['ausencia', 'no asistirá', 'no va', 'enfermo', 'enfermedad', 'falta', 'faltar'];
const palabrasConsulta = ['horario', 'cuando', 'qué hora', 'información', 'pregunta'];
const palabrasReporte = ['reporte', 'problema', 'comportamiento', 'incidente', 'queja'];

let tipoMensaje = 'general';
let confianza = 0.5;
let respuestaAutomatica = 'Gracias por contactarnos. Su mensaje ha sido recibido.';

// Detectar tipo de mensaje
const mensajeLower = mensaje.toLowerCase();

if (palabrasAusencia.some(palabra => mensajeLower.includes(palabra))) {
  tipoMensaje = 'ausencia';
  confianza = 0.9;
  respuestaAutomatica = '✅ Hemos registrado el reporte de ausencia. El docente será notificado automáticamente.';
} else if (palabrasConsulta.some(palabra => mensajeLower.includes(palabra))) {
  tipoMensaje = 'consulta';
  confianza = 0.8;
  respuestaAutomatica = '📚 Horario de clases: 7:00 AM - 12:00 PM. Horario de oficina: 7:00 AM - 3:00 PM.';
} else if (palabrasReporte.some(palabra => mensajeLower.includes(palabra))) {
  tipoMensaje = 'reporte_comportamiento';
  confianza = 0.85;
  respuestaAutomatica = '📋 Su reporte ha sido recibido. El director lo revisará y se pondrá en contacto con usted.';
}

return [{
  json: {
    tipo_mensaje: tipoMensaje,
    confianza: confianza,
    respuesta_automatica: respuestaAutomatica,
    mensaje_original: mensaje,
    telefono: telefono,
    nombre: nombre,
    timestamp: new Date().toISOString()
  }
}];
`
        },
        id: 'analyze-message',
        name: 'Análisis IA',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [680, 300]
      },
      
      // 4. Actualizar mensaje con análisis
      {
        parameters: {
          operation: 'update',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              tipo_mensaje: '={{ $json.tipo_mensaje }}',
              ai_analysis: '={{ JSON.stringify($json) }}',
              confidence_score: '={{ $json.confianza }}',
              procesado: true
            }
          },
          table: 'mensajes_whatsapp',
          updateKey: 'id',
          columnToMatchOn: 'telefono_remitente',
          valueToMatchOn: '={{ $json.telefono }}'
        },
        id: 'update-message',
        name: 'Actualizar Análisis',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [900, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 5. Respuesta automática
      {
        parameters: {
          jsCode: `
// Preparar respuesta
const data = $input.first().json;

return [{
  json: {
    phone: data.telefono,
    message: data.respuesta_automatica,
    type: data.tipo_mensaje
  }
}];
`
        },
        id: 'prepare-response',
        name: 'Preparar Respuesta',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1120, 300]
      },
      
      // 6. Nodo de respuesta
      {
        parameters: {
          options: {}
        },
        id: 'webhook-response',
        name: 'Respuesta Webhook',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [1340, 300]
      }
    ],
    
    connections: {
      'WhatsApp Webhook': {
        main: [
          [
            {
              node: 'Guardar Mensaje',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Guardar Mensaje': {
        main: [
          [
            {
              node: 'Análisis IA',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Análisis IA': {
        main: [
          [
            {
              node: 'Actualizar Análisis',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Actualizar Análisis': {
        main: [
          [
            {
              node: 'Preparar Respuesta',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Respuesta': {
        main: [
          [
            {
              node: 'Respuesta Webhook',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow principal creado con ID: ${result.id}`);
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow principal:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow principal
    const mainWorkflowId = await createMainWorkflow();
    
    console.log('\n🎉 ¡Workflows creados exitosamente!');
    console.log('📋 Resumen:');
    console.log(`   - Workflow Principal: ${mainWorkflowId}`);
    console.log('\n🔗 URLs de Webhook:');
    console.log(`   - Principal: ${N8N_BASE_URL}/webhook/whatsapp-main`);
    console.log('\n📝 Próximos pasos:');
    console.log('   1. Acceder a n8n: http://localhost:5678');
    console.log('   2. Verificar que el workflow esté activo');
    console.log('   3. Configurar credenciales de PostgreSQL si es necesario');
    console.log('   4. Probar enviando un mensaje al webhook');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
