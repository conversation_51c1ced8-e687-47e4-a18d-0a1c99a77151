const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const { Pool } = require('pg');

// Configuración
const N8N_BASE_URL = process.env.N8N_URL || 'http://localhost:5678';
const N8N_API_KEY = process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';
const CHATGPT_API_KEY = process.env.CHATGPT_API_KEY || 'sk-your-openai-api-key-here';

// Configuración de base de datos
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'ccjap_db',
  user: process.env.DB_USER || 'ccjap_admin',
  password: process.env.DB_PASSWORD || 'K@rur0su24'
};

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// 1. Crear credenciales de PostgreSQL en n8n
async function createPostgresCredentials() {
  console.log("Creando credenciales de PostgreSQL en n8n...");
  
  const credentialData = {
    name: 'PostgreSQL CCJAP',
    type: 'postgres',
    data: {
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.user,
      password: dbConfig.password,
      ssl: 'disable'
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/credentials', credentialData);
    console.log(`✅ Credenciales PostgreSQL creadas con ID: ${result.id}`);
    return result.id;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      console.log("⚠️ Las credenciales PostgreSQL ya existen");
      return null;
    }
    throw error;
  }
}

// 2. Crear credenciales de OpenAI en n8n
async function createOpenAICredentials() {
  console.log("Creando credenciales de OpenAI en n8n...");
  
  const credentialData = {
    name: 'OpenAI CCJAP',
    type: 'openAiApi',
    data: {
      apiKey: CHATGPT_API_KEY
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/credentials', credentialData);
    console.log(`✅ Credenciales OpenAI creadas con ID: ${result.id}`);
    return result.id;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      console.log("⚠️ Las credenciales OpenAI ya existen");
      return null;
    }
    throw error;
  }
}

// 3. Ejecutar script SQL para crear tablas
async function createDatabaseTables() {
  console.log("Creando tablas de base de datos...");
  
  const pool = new Pool(dbConfig);
  
  try {
    const sqlPath = path.join(__dirname, 'create_ai_tables.sql');
    const sqlContent = await fs.readFile(sqlPath, 'utf8');
    
    await pool.query(sqlContent);
    console.log("✅ Tablas de base de datos creadas exitosamente");
  } catch (error) {
    console.error("❌ Error creando tablas:", error);
    throw error;
  } finally {
    await pool.end();
  }
}

// 4. Configurar webhooks en WaAPI
async function configureWaAPIWebhooks() {
  console.log("Configurando webhooks de WaAPI...");
  
  const webhookUrls = {
    main: `${N8N_BASE_URL}/webhook/whatsapp-main`,
    absence: `${N8N_BASE_URL}/webhook/process-absence`,
    behavior: `${N8N_BASE_URL}/webhook/behavior-report`
  };
  
  console.log("🔗 URLs de webhook configuradas:");
  Object.entries(webhookUrls).forEach(([key, url]) => {
    console.log(`  - ${key}: ${url}`);
  });
  
  return webhookUrls;
}

// 5. Crear configuración inicial en base de datos
async function createInitialConfiguration() {
  console.log("Creando configuración inicial...");
  
  const pool = new Pool(dbConfig);
  
  try {
    // Insertar configuración de horarios por defecto
    await pool.query(`
      INSERT INTO configuracion_horarios (institucion_id, tipo_notificacion, hora_envio, dias_semana)
      SELECT 1, 'ausencias_diarias', '08:00', '{1,2,3,4,5}'
      WHERE NOT EXISTS (
        SELECT 1 FROM configuracion_horarios 
        WHERE institucion_id = 1 AND tipo_notificacion = 'ausencias_diarias'
      )
    `);
    
    // Actualizar configuración de WaAPI con URLs de n8n
    await pool.query(`
      UPDATE waapi_config 
      SET n8n_url = $1, 
          webhook_url = $2,
          n8n_api_key = $3
      WHERE institucion_id = 1
    `, [N8N_BASE_URL, `${N8N_BASE_URL}/webhook/whatsapp-main`, N8N_API_KEY]);
    
    console.log("✅ Configuración inicial creada");
  } catch (error) {
    console.error("❌ Error en configuración inicial:", error);
    throw error;
  } finally {
    await pool.end();
  }
}

// 6. Función principal de configuración
async function setupCompleteAISystem() {
  try {
    console.log("🚀 Iniciando configuración completa del sistema de IA...");
    console.log("=" .repeat(60));
    
    // Paso 1: Crear tablas de base de datos
    await createDatabaseTables();
    
    // Paso 2: Crear credenciales en n8n
    await createPostgresCredentials();
    await createOpenAICredentials();
    
    // Paso 3: Crear workflows
    const { createCompleteAISystem } = require('./create_complete_ai_system');
    const workflowResults = await createCompleteAISystem();
    
    // Paso 4: Configurar webhooks
    const webhookUrls = await configureWaAPIWebhooks();
    
    // Paso 5: Configuración inicial
    await createInitialConfiguration();
    
    console.log("=" .repeat(60));
    console.log("🎉 ¡Sistema de IA configurado exitosamente!");
    console.log("=" .repeat(60));
    
    console.log("\n📋 RESUMEN DE CONFIGURACIÓN:");
    console.log(`✅ Workflows creados: ${Object.keys(workflowResults).length}`);
    console.log(`✅ Webhooks configurados: ${Object.keys(webhookUrls).length}`);
    console.log(`✅ Tablas de BD creadas: ✓`);
    console.log(`✅ Credenciales n8n: ✓`);
    
    console.log("\n🔧 PRÓXIMOS PASOS:");
    console.log("1. Configurar la API key de ChatGPT en las credenciales de n8n");
    console.log("2. Configurar las credenciales de WaAPI en n8n");
    console.log("3. Activar los workflows en n8n");
    console.log("4. Configurar el webhook principal en WaAPI");
    
    console.log("\n🌐 URLs IMPORTANTES:");
    console.log(`- n8n Dashboard: ${N8N_BASE_URL}`);
    console.log(`- Webhook Principal: ${webhookUrls.main}`);
    
    return {
      workflows: workflowResults,
      webhooks: webhookUrls,
      status: 'success'
    };
    
  } catch (error) {
    console.error("💥 Error en la configuración del sistema:", error);
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  setupCompleteAISystem()
    .then((result) => {
      console.log("\n🎊 ¡Configuración completada exitosamente!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💀 Error fatal en la configuración:", error);
      process.exit(1);
    });
}

module.exports = {
  setupCompleteAISystem,
  createPostgresCredentials,
  createOpenAICredentials,
  createDatabaseTables
};
