import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

const GestionGruposCompleta = () => {
  const [activeTab, setActiveTab] = useState('grados');
  const [grados, setGrados] = useState([]);
  const [alumnos, setAlumnos] = useState([]);
  const [docentes, setDocentes] = useState([]);
  const [gruposWhatsApp, setGruposWhatsApp] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const { token } = useAuth();

  // Estados para formularios
  const [gradoForm, setGradoForm] = useState({
    nombre: '',
    nivel: '',
    seccion: '',
    capacidad_maxima: 30
  });

  const [asignacionForm, setAsignacionForm] = useState({
    alumno_id: '',
    grado_id: '',
    docente_id: '',
    es_tutor: false
  });

  useEffect(() => {
    fetchAllData();
  }, [token]);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchGrados(),
        fetchAlumnos(),
        fetchDocentes(),
        fetchGruposWhatsApp()
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchGrados = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/grupos/grados`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setGrados(data);
    } catch (error) {
      console.error('Error fetching grados:', error);
    }
  };

  const fetchAlumnos = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/alumnos`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setAlumnos(data);
    } catch (error) {
      console.error('Error fetching alumnos:', error);
    }
  };

  const fetchDocentes = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/grupos/docentes`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setDocentes(data);
    } catch (error) {
      console.error('Error fetching docentes:', error);
    }
  };

  const fetchGruposWhatsApp = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/grupos/whatsapp`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setGruposWhatsApp(data);
    } catch (error) {
      console.error('Error fetching grupos WhatsApp:', error);
    }
  };

  const handleCreateGrado = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch(`${API_BASE_URL}/api/grupos/grados`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(gradoForm)
      });

      if (response.ok) {
        await fetchGrados();
        setShowModal(false);
        setGradoForm({ nombre: '', nivel: '', seccion: '', capacidad_maxima: 30 });
      }
    } catch (error) {
      console.error('Error creating grado:', error);
    }
  };

  const handleAsignAlumno = async (alumnoId, gradoId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/alumnos/${alumnoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ grado_id: gradoId })
      });

      if (response.ok) {
        await fetchAlumnos();
      }
    } catch (error) {
      console.error('Error assigning alumno:', error);
    }
  };

  const openModal = (type, item = null) => {
    setModalType(type);
    setSelectedItem(item);
    setShowModal(true);

    if (type === 'editGrado' && item) {
      setGradoForm({
        nombre: item.nombre,
        nivel: item.nivel,
        seccion: item.seccion,
        capacidad_maxima: item.capacidad_maxima
      });
    }
  };

  const renderModalContent = () => {
    switch (modalType) {
      case 'createGrado':
      case 'editGrado':
        return (
          <form onSubmit={handleCreateGrado} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Nombre del Grado
              </label>
              <input
                type="text"
                value={gradoForm.nombre}
                onChange={(e) => setGradoForm(prev => ({ ...prev, nombre: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                placeholder="Ej: Primer Grado, Kinder, etc."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Nivel
              </label>
              <select
                value={gradoForm.nivel}
                onChange={(e) => setGradoForm(prev => ({ ...prev, nivel: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                required
              >
                <option value="">Seleccionar nivel...</option>
                <option value="Preescolar">Preescolar</option>
                <option value="Primaria">Primaria</option>
                <option value="Secundaria">Secundaria</option>
                <option value="Bachillerato">Bachillerato</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Sección
              </label>
              <input
                type="text"
                value={gradoForm.seccion}
                onChange={(e) => setGradoForm(prev => ({ ...prev, seccion: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                placeholder="Ej: A, B, C, etc."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Capacidad Máxima
              </label>
              <input
                type="number"
                value={gradoForm.capacidad_maxima}
                onChange={(e) => setGradoForm(prev => ({ ...prev, capacidad_maxima: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                min="1"
                max="50"
                required
              />
            </div>
          </form>
        );

      case 'assignStudents':
        return <AssignStudentsModal />;

      case 'assignTeacher':
        return <AssignTeacherModal />;

      case 'createWhatsAppGroup':
        return <CreateWhatsAppGroupModal />;

      case 'manageGroupMembers':
        return <ManageGroupMembersModal />;

      default:
        return <div>Contenido del modal no implementado</div>;
    }
  };

  const AssignStudentsModal = () => {
    const [selectedStudents, setSelectedStudents] = useState([]);
    const alumnosSinAsignar = alumnos.filter(alumno => !alumno.grado_id);

    const handleAssignStudents = async () => {
      if (selectedStudents.length === 0) return;

      try {
        const assignments = selectedStudents.map(studentId => ({
          alumno_id: studentId,
          grado_id: selectedItem.id
        }));

        const response = await fetch(`${API_BASE_URL}/api/alumnos/bulk-assign`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ assignments })
        });

        if (response.ok) {
          await fetchAlumnos();
          await fetchGrados();
          setShowModal(false);
          setSelectedStudents([]);
        }
      } catch (error) {
        console.error('Error assigning students:', error);
      }
    };

    return (
      <div className="space-y-4">
        <p className="text-sm text-slate-600 dark:text-slate-400">
          Selecciona los alumnos que deseas asignar a este grado:
        </p>

        <div className="max-h-60 overflow-y-auto border border-slate-200 dark:border-slate-600 rounded-lg">
          {alumnosSinAsignar.length === 0 ? (
            <p className="p-4 text-center text-slate-500 dark:text-slate-400">
              No hay alumnos sin asignar
            </p>
          ) : (
            <div className="p-2 space-y-2">
              {alumnosSinAsignar.map(alumno => (
                <label key={alumno.id} className="flex items-center space-x-3 p-2 hover:bg-slate-50 dark:hover:bg-slate-700 rounded">
                  <input
                    type="checkbox"
                    checked={selectedStudents.includes(alumno.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedStudents(prev => [...prev, alumno.id]);
                      } else {
                        setSelectedStudents(prev => prev.filter(id => id !== alumno.id));
                      }
                    }}
                    className="rounded border-slate-300 dark:border-slate-600"
                  />
                  <div className="flex-1">
                    <p className="font-medium text-slate-700 dark:text-slate-200">
                      {alumno.nombre_completo}
                    </p>
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      {alumno.grado_actual} - {alumno.seccion}
                    </p>
                  </div>
                </label>
              ))}
            </div>
          )}
        </div>

        {selectedStudents.length > 0 && (
          <div className="flex justify-end">
            <button
              onClick={handleAssignStudents}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Asignar {selectedStudents.length} alumno(s)
            </button>
          </div>
        )}
      </div>
    );
  };

  const AssignTeacherModal = () => {
    const [teacherForm, setTeacherForm] = useState({
      docente_id: '',
      materia: '',
      es_tutor: false
    });

    const handleAssignTeacher = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/grupos/asignaciones`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            ...teacherForm,
            grado_id: selectedItem.id
          })
        });

        if (response.ok) {
          await fetchGrados();
          setShowModal(false);
          setTeacherForm({ docente_id: '', materia: '', es_tutor: false });
        }
      } catch (error) {
        console.error('Error assigning teacher:', error);
      }
    };

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
            Docente
          </label>
          <select
            value={teacherForm.docente_id}
            onChange={(e) => setTeacherForm(prev => ({ ...prev, docente_id: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
            required
          >
            <option value="">Seleccionar docente...</option>
            {docentes.map(docente => (
              <option key={docente.id} value={docente.id}>
                {docente.nombre} - {docente.rol}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
            Materia
          </label>
          <input
            type="text"
            value={teacherForm.materia}
            onChange={(e) => setTeacherForm(prev => ({ ...prev, materia: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
            placeholder="Ej: Matemáticas, Español, etc."
          />
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="es_tutor"
            checked={teacherForm.es_tutor}
            onChange={(e) => setTeacherForm(prev => ({ ...prev, es_tutor: e.target.checked }))}
            className="rounded border-slate-300 dark:border-slate-600"
          />
          <label htmlFor="es_tutor" className="text-sm text-slate-700 dark:text-slate-200">
            Es tutor principal del grado
          </label>
        </div>

        <div className="flex justify-end">
          <button
            onClick={handleAssignTeacher}
            disabled={!teacherForm.docente_id}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-slate-400"
          >
            Asignar Docente
          </button>
        </div>
      </div>
    );
  };

  const CreateWhatsAppGroupModal = () => {
    const [groupForm, setGroupForm] = useState({
      nombre: '',
      descripcion: '',
      tipo: 'grado',
      grado_id: ''
    });

    const handleCreateGroup = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/grupos/whatsapp`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(groupForm)
        });

        if (response.ok) {
          await fetchGruposWhatsApp();
          setShowModal(false);
          setGroupForm({ nombre: '', descripcion: '', tipo: 'grado', grado_id: '' });
        }
      } catch (error) {
        console.error('Error creating WhatsApp group:', error);
      }
    };

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
            Nombre del Grupo
          </label>
          <input
            type="text"
            value={groupForm.nombre}
            onChange={(e) => setGroupForm(prev => ({ ...prev, nombre: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
            placeholder="Ej: Padres 1er Grado A"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
            Tipo de Grupo
          </label>
          <select
            value={groupForm.tipo}
            onChange={(e) => setGroupForm(prev => ({ ...prev, tipo: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
          >
            <option value="grado">Padres de Grado</option>
            <option value="docentes">Docentes</option>
            <option value="administrativo">Administrativo</option>
          </select>
        </div>

        {groupForm.tipo === 'grado' && (
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
              Grado Asociado
            </label>
            <select
              value={groupForm.grado_id}
              onChange={(e) => setGroupForm(prev => ({ ...prev, grado_id: e.target.value }))}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
              required
            >
              <option value="">Seleccionar grado...</option>
              {grados.map(grado => (
                <option key={grado.id} value={grado.id}>
                  {grado.nombre} {grado.seccion}
                </option>
              ))}
            </select>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
            Descripción
          </label>
          <textarea
            value={groupForm.descripcion}
            onChange={(e) => setGroupForm(prev => ({ ...prev, descripcion: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
            rows="3"
            placeholder="Descripción del grupo..."
          />
        </div>

        <div className="flex justify-end">
          <button
            onClick={handleCreateGroup}
            disabled={!groupForm.nombre}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-slate-400"
          >
            Crear Grupo
          </button>
        </div>
      </div>
    );
  };

  const ManageGroupMembersModal = () => {
    const [members, setMembers] = useState([]);
    const [newMember, setNewMember] = useState({ telefono: '', nombre: '' });
    const [loadingMembers, setLoadingMembers] = useState(true);

    useEffect(() => {
      if (selectedItem?.id) {
        fetchGroupMembers();
      }
    }, [selectedItem]);

    const fetchGroupMembers = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/grupos/whatsapp/${selectedItem.id}/miembros`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        const data = await response.json();
        setMembers(data);
      } catch (error) {
        console.error('Error fetching members:', error);
      } finally {
        setLoadingMembers(false);
      }
    };

    const handleAddMember = async () => {
      if (!newMember.telefono || !newMember.nombre) return;

      try {
        const response = await fetch(`${API_BASE_URL}/api/grupos/whatsapp/${selectedItem.id}/miembros`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(newMember)
        });

        if (response.ok) {
          await fetchGroupMembers();
          setNewMember({ telefono: '', nombre: '' });
        }
      } catch (error) {
        console.error('Error adding member:', error);
      }
    };

    const handleAutoAssignParents = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/grupos/auto-assign-parents`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          await fetchGroupMembers();
          await fetchGruposWhatsApp();
        }
      } catch (error) {
        console.error('Error auto-assigning parents:', error);
      }
    };

    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h4 className="font-medium text-slate-700 dark:text-slate-200">
            Miembros Actuales ({members.length})
          </h4>
          <button
            onClick={handleAutoAssignParents}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Auto-asignar Padres
          </button>
        </div>

        <div className="max-h-40 overflow-y-auto border border-slate-200 dark:border-slate-600 rounded-lg">
          {loadingMembers ? (
            <p className="p-4 text-center text-slate-500">Cargando...</p>
          ) : members.length === 0 ? (
            <p className="p-4 text-center text-slate-500">No hay miembros en este grupo</p>
          ) : (
            <div className="p-2 space-y-2">
              {members.map(member => (
                <div key={member.id} className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-700 rounded">
                  <div>
                    <p className="font-medium text-slate-700 dark:text-slate-200">{member.nombre}</p>
                    <p className="text-sm text-slate-500 dark:text-slate-400">{member.telefono}</p>
                  </div>
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded">
                    {member.rol}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="border-t border-slate-200 dark:border-slate-600 pt-4">
          <h5 className="font-medium text-slate-700 dark:text-slate-200 mb-3">Agregar Nuevo Miembro</h5>
          <div className="space-y-3">
            <input
              type="text"
              value={newMember.nombre}
              onChange={(e) => setNewMember(prev => ({ ...prev, nombre: e.target.value }))}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
              placeholder="Nombre completo"
            />
            <input
              type="tel"
              value={newMember.telefono}
              onChange={(e) => setNewMember(prev => ({ ...prev, telefono: e.target.value }))}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
              placeholder="Número de teléfono"
            />
            <button
              onClick={handleAddMember}
              disabled={!newMember.telefono || !newMember.nombre}
              className="w-full px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-slate-400"
            >
              Agregar Miembro
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'grados':
        return <GradosTab />;
      case 'asignaciones':
        return <AsignacionesTab />;
      case 'whatsapp':
        return <WhatsAppTab />;
      default:
        return <GradosTab />;
    }
  };

  const GradosTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200">
          Gestión de Grados
        </h3>
        <button
          onClick={() => openModal('createGrado')}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          + Crear Grado
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {grados.map(grado => (
          <div key={grado.id} className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h4 className="font-semibold text-slate-700 dark:text-slate-200">
                  {grado.nombre} {grado.seccion}
                </h4>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  {grado.nivel}
                </p>
              </div>
              <button
                onClick={() => openModal('editGrado', grado)}
                className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
              >
                ✏️
              </button>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-600 dark:text-slate-400">Alumnos:</span>
                <span className="font-medium text-slate-700 dark:text-slate-200">
                  {grado.alumnos_activos || 0} / {grado.capacidad_maxima}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-600 dark:text-slate-400">Tutor:</span>
                <span className="font-medium text-slate-700 dark:text-slate-200">
                  {grado.tutor_nombre || 'Sin asignar'}
                </span>
              </div>
            </div>

            <div className="mt-4 flex space-x-2">
              <button
                onClick={() => openModal('assignStudents', grado)}
                className="flex-1 px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200 transition-colors"
              >
                Asignar Alumnos
              </button>
              <button
                onClick={() => openModal('assignTeacher', grado)}
                className="flex-1 px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200 transition-colors"
              >
                Asignar Docente
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const AsignacionesTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200">
        Asignaciones de Alumnos
      </h3>
      
      <div className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
        <div className="p-4 border-b border-slate-200 dark:border-slate-700">
          <h4 className="font-medium text-slate-700 dark:text-slate-200">
            Alumnos sin Asignar
          </h4>
        </div>
        <div className="p-4">
          <div className="space-y-3">
            {alumnos.filter(alumno => !alumno.grado_id).map(alumno => (
              <div key={alumno.id} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <div>
                  <p className="font-medium text-slate-700 dark:text-slate-200">
                    {alumno.nombre_completo}
                  </p>
                  <p className="text-sm text-slate-500 dark:text-slate-400">
                    {alumno.grado_actual} - {alumno.seccion}
                  </p>
                </div>
                <select
                  onChange={(e) => e.target.value && handleAsignAlumno(alumno.id, e.target.value)}
                  className="px-3 py-1 border border-slate-300 dark:border-slate-600 rounded bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                >
                  <option value="">Asignar a grado...</option>
                  {grados.map(grado => (
                    <option key={grado.id} value={grado.id}>
                      {grado.nombre} {grado.seccion}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const WhatsAppTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200">
          Grupos de WhatsApp
        </h3>
        <button
          onClick={() => openModal('createWhatsAppGroup')}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          + Crear Grupo WhatsApp
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {gruposWhatsApp.map(grupo => (
          <div key={grupo.id} className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4">
            <div className="flex items-center space-x-3 mb-3">
              <span className="text-2xl">💬</span>
              <div>
                <h4 className="font-semibold text-slate-700 dark:text-slate-200">
                  {grupo.nombre}
                </h4>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  {grupo.grado_nombre} - {grupo.total_miembros} miembros
                </p>
              </div>
            </div>
            
            <button
              onClick={() => openModal('manageGroupMembers', grupo)}
              className="w-full px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            >
              Gestionar Miembros
            </button>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-slate-600 dark:text-slate-400">Cargando...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700">
        <div className="p-6 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-2xl font-bold text-slate-700 dark:text-slate-200">
            Gestión Completa de Grupos
          </h2>
          <p className="text-slate-600 dark:text-slate-400 mt-2">
            Administra grados, asigna alumnos y gestiona grupos de WhatsApp
          </p>
        </div>

        {/* Tabs */}
        <div className="border-b border-slate-200 dark:border-slate-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'grados', label: 'Grados', icon: '🏫' },
              { id: 'asignaciones', label: 'Asignaciones', icon: '👥' },
              { id: 'whatsapp', label: 'WhatsApp', icon: '💬' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                    : 'border-transparent text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>

      {/* Modales */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4 text-slate-700 dark:text-slate-200">
              {modalType === 'createGrado' && 'Crear Nuevo Grado'}
              {modalType === 'editGrado' && 'Editar Grado'}
              {modalType === 'assignStudents' && `Asignar Alumnos a ${selectedItem?.nombre} ${selectedItem?.seccion}`}
              {modalType === 'assignTeacher' && `Asignar Docente a ${selectedItem?.nombre} ${selectedItem?.seccion}`}
              {modalType === 'createWhatsAppGroup' && 'Crear Grupo WhatsApp'}
              {modalType === 'manageGroupMembers' && `Gestionar Miembros - ${selectedItem?.nombre}`}
            </h3>

            {renderModalContent()}

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200"
              >
                Cancelar
              </button>
              {(modalType === 'createGrado' || modalType === 'editGrado') && (
                <button
                  onClick={handleCreateGrado}
                  className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                >
                  {modalType === 'createGrado' ? 'Crear' : 'Actualizar'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionGruposCompleta;
