const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🤖 Creando workflow con AI Agent + Memory Manager...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow con AI Agent y Memory Manager
async function createAIAgentWorkflow() {
  console.log('\n🧠 Creando workflow con AI Agent y Memory Manager...');
  
  const workflowData = {
    name: 'WhatsApp AI Agent - Con Memoria Avanzada',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Chat Trigger para conversaciones
      {
        parameters: {
          mode: 'webhook',
          options: {
            webhookPath: 'ai-agent'
          }
        },
        id: 'chat-trigger',
        name: 'Chat Trigger',
        type: 'n8n-nodes-langchain.chatTrigger',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Memory Manager para mantener conversaciones
      {
        parameters: {
          sessionIdType: 'customKey',
          sessionKey: '={{ $json.sessionId || $json.from || "default" }}',
          contextWindowLength: 10
        },
        id: 'memory-manager',
        name: 'Memory Manager',
        type: 'n8n-nodes-langchain.memoryManager',
        typeVersion: 1,
        position: [460, 200]
      },
      
      // 3. Window Buffer Memory
      {
        parameters: {
          contextWindowLength: 10,
          returnMessages: true
        },
        id: 'window-memory',
        name: 'Window Memory',
        type: 'n8n-nodes-langchain.memoryBufferWindow',
        typeVersion: 1,
        position: [460, 100]
      },
      
      // 4. OpenAI Chat Model
      {
        parameters: {
          model: 'gpt-4',
          options: {
            temperature: 0.7,
            maxTokens: 1000,
            topP: 1
          }
        },
        id: 'openai-model',
        name: 'OpenAI GPT-4',
        type: 'n8n-nodes-langchain.lmChatOpenAi',
        typeVersion: 1,
        position: [680, 100],
        credentials: {
          openAiApi: {
            id: 'openai-ccjap',
            name: 'OpenAI CCJAP'
          }
        }
      },
      
      // 5. AI Agent principal
      {
        parameters: {
          agent: 'conversationalAgent',
          promptType: 'define',
          text: `Eres el asistente inteligente del Colegio Cristiano Jerusalén de los Altos de Palencia (CCJAP).

INFORMACIÓN DEL COLEGIO:
- Nombre: Colegio Cristiano Jerusalén de los Altos de Palencia
- Horario de clases: 7:00 AM - 12:00 PM (Lunes a Viernes)
- Horario de oficina: 7:00 AM - 3:00 PM (Lunes a Viernes)
- Teléfono: +503 1234-5678
- Ubicación: Palencia, El Salvador

CAPACIDADES PRINCIPALES:
1. AUSENCIAS: Procesar reportes de inasistencia estudiantil
2. CONSULTAS: Responder sobre horarios, actividades, información general
3. REPORTES: Gestionar reportes de comportamiento con evidencia
4. COMUNICACIÓN: Facilitar comunicación entre padres, docentes y dirección

TIPOS DE MENSAJE A CLASIFICAR:
- "ausencia": Reporte de inasistencia estudiantil
- "consulta": Pregunta sobre información del colegio
- "reporte_comportamiento": Reporte de comportamiento con/sin evidencia
- "director_atencion": Requiere intervención urgente del director
- "general": Mensaje general o saludo

INSTRUCCIONES ESPECÍFICAS:
- Siempre responde de forma profesional, empática y útil
- Para ausencias: confirma recepción, solicita detalles si es necesario, y notifica que se informará al docente
- Para consultas: proporciona información precisa y completa del colegio
- Para reportes: confirma recepción, explica el proceso de revisión, y tranquiliza al padre
- Mantén un tono cálido pero profesional
- Usa emojis apropiados para hacer la comunicación más amigable
- Si no entiendes algo, pide aclaración de forma cortés
- Recuerda conversaciones anteriores y haz referencia a ellas cuando sea apropiado

EJEMPLOS DE RESPUESTAS:

Para ausencias:
"✅ He registrado la ausencia de [nombre del estudiante]. El docente será notificado automáticamente. ¿Necesita que le proporcione alguna información adicional sobre tareas o actividades perdidas?"

Para consultas:
"📚 Con gusto le ayudo con esa información. [Respuesta específica]. ¿Hay algo más en lo que pueda asistirle?"

Para reportes:
"📋 He recibido su reporte sobre [situación]. La dirección revisará la información y se pondrá en contacto con usted dentro de las próximas 24 horas. ¿Hay algún detalle adicional que considere importante agregar?"

Mantén siempre la memoria de la conversación y personaliza las respuestas según el contexto previo.`,
          options: {
            systemMessage: 'Recuerda que tienes acceso a la memoria de conversaciones anteriores. Úsala para proporcionar respuestas más personalizadas y contextuales.'
          }
        },
        id: 'ai-agent',
        name: 'AI Agent CCJAP',
        type: 'n8n-nodes-langchain.agent',
        typeVersion: 1,
        position: [900, 300]
      },
      
      // 6. Procesar respuesta del agente
      {
        parameters: {
          jsCode: `
// Procesar respuesta del AI Agent y extraer información
const agentResponse = $input.first().json;
const originalInput = $('Chat Trigger').first().json;

// Extraer información del mensaje original
const mensaje = originalInput.chatInput || originalInput.body || '';
const telefono = originalInput.from || originalInput.sessionId || 'unknown';
const nombre = originalInput.name || 'Usuario';

// Análisis del tipo de mensaje basado en la respuesta del agente
let tipoMensaje = 'general';
let confianza = 0.8;
let requiereAprobacion = false;

const mensajeLower = mensaje.toLowerCase();
const respuestaLower = (agentResponse.output || '').toLowerCase();

// Clasificación inteligente basada en contenido
if (mensajeLower.includes('ausencia') || mensajeLower.includes('no asistirá') || mensajeLower.includes('enfermo') || 
    respuestaLower.includes('ausencia') || respuestaLower.includes('registrado')) {
  tipoMensaje = 'ausencia';
  confianza = 0.95;
  requiereAprobacion = false;
} else if (mensajeLower.includes('horario') || mensajeLower.includes('información') || mensajeLower.includes('pregunta') ||
           respuestaLower.includes('información') || respuestaLower.includes('horario')) {
  tipoMensaje = 'consulta';
  confianza = 0.9;
  requiereAprobacion = false;
} else if (mensajeLower.includes('reporte') || mensajeLower.includes('problema') || mensajeLower.includes('comportamiento') ||
           respuestaLower.includes('reporte') || respuestaLower.includes('revisará')) {
  tipoMensaje = 'reporte_comportamiento';
  confianza = 0.9;
  requiereAprobacion = true;
} else if (mensajeLower.includes('urgente') || mensajeLower.includes('director') ||
           respuestaLower.includes('urgente') || respuestaLower.includes('director')) {
  tipoMensaje = 'director_atencion';
  confianza = 0.85;
  requiereAprobacion = true;
}

const analisisCompleto = {
  mensaje_original: mensaje,
  respuesta_agente: agentResponse.output || agentResponse.text || 'Sin respuesta',
  clasificacion: {
    tipo_mensaje: tipoMensaje,
    confianza: confianza,
    requiere_aprobacion: requiereAprobacion,
    procesado_por: 'ai-agent-gpt4'
  },
  remitente: {
    telefono: telefono,
    nombre: nombre
  },
  session_id: originalInput.sessionId || telefono,
  timestamp: new Date().toISOString(),
  memoria_utilizada: true
};

console.log('🤖 Análisis AI Agent completado:', JSON.stringify(analisisCompleto, null, 2));

return [{
  json: analisisCompleto
}];
`
        },
        id: 'process-agent-response',
        name: 'Procesar Respuesta Agent',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1120, 300]
      },
      
      // 7. Guardar en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.remitente.telefono }}',
              nombre_remitente: '={{ $json.remitente.nombre }}',
              texto_mensaje: '={{ $json.mensaje_original }}',
              tipo_mensaje: '={{ $json.clasificacion.tipo_mensaje }}',
              ai_analysis: '={{ JSON.stringify($json) }}',
              confidence_score: '={{ $json.clasificacion.confianza }}',
              requires_approval: '={{ $json.clasificacion.requiere_aprobacion }}',
              response_message: '={{ $json.respuesta_agente }}',
              procesado: true,
              fecha_recepcion: '={{ new Date().toISOString() }}'
            }
          },
          table: 'mensajes_whatsapp'
        },
        id: 'save-agent-data',
        name: 'Guardar en BD',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1340, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      }
    ],
    
    connections: {
      'Chat Trigger': {
        main: [
          [
            {
              node: 'AI Agent CCJAP',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Memory Manager': {
        main: [
          [
            {
              node: 'AI Agent CCJAP',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Window Memory': {
        main: [
          [
            {
              node: 'Memory Manager',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'OpenAI GPT-4': {
        main: [
          [
            {
              node: 'Window Memory',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'AI Agent CCJAP': {
        main: [
          [
            {
              node: 'Procesar Respuesta Agent',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar Respuesta Agent': {
        main: [
          [
            {
              node: 'Guardar en BD',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow con AI Agent creado con ID: ${result.id}`);
    
    // Activar el workflow
    await makeN8nRequest('POST', `/workflows/${result.id}/activate`);
    console.log(`✅ Workflow activado automáticamente`);
    
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow con AI Agent:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow con AI Agent
    const agentWorkflowId = await createAIAgentWorkflow();
    
    console.log('\n🎉 ¡Workflow con AI Agent creado exitosamente!');
    console.log('📋 Características implementadas:');
    console.log('   ✅ AI Agent con GPT-4');
    console.log('   ✅ Memory Manager para conversaciones');
    console.log('   ✅ Window Buffer Memory (10 mensajes)');
    console.log('   ✅ Chat Trigger para interacciones');
    console.log('   ✅ Análisis inteligente contextual');
    console.log('   ✅ Almacenamiento en base de datos');
    
    console.log('\n🔗 URL de Webhook:');
    console.log(`   - AI Agent: ${N8N_BASE_URL}/webhook/ai-agent`);
    
    console.log('\n🧪 Ejemplo de prueba:');
    console.log('curl -X POST http://localhost:5678/webhook/ai-agent \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"chatInput": "Hola, mi hijo Juan no asistirá hoy por enfermedad", "sessionId": "50312345678", "from": "50312345678", "name": "María González"}\'');
    
    console.log('\n💡 VENTAJAS del AI Agent:');
    console.log('✅ Memoria persistente entre conversaciones');
    console.log('✅ Respuestas más naturales y contextuales');
    console.log('✅ Mejor comprensión de intenciones');
    console.log('✅ Capacidad de seguimiento de temas');
    console.log('✅ Personalización basada en historial');
    
    console.log('\n⚠️ IMPORTANTE:');
    console.log('1. Configurar credencial OpenAI en n8n');
    console.log('2. Configurar credencial PostgreSQL en n8n');
    console.log('3. El sessionId mantiene la memoria por usuario');
    console.log('4. Usar chatInput para el mensaje del usuario');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
