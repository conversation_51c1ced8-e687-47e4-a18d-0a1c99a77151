/**
 * Configuración de Entorno - Sistema IA WhatsApp
 * Colegio Cristiano Jerusalén de los Altos de Palencia
 * 
 * IMPORTANTE: Este archivo contiene configuraciones para DESARROLLO LOCAL
 * Para producción, actualizar las URLs y configuraciones correspondientes
 */

const isDevelopment = process.env.NODE_ENV !== 'production';
const isProduction = process.env.NODE_ENV === 'production';

// Configuración base según entorno
const config = {
  development: {
    // URLs de servicios locales
    frontend: {
      url: 'http://localhost:5173',
      port: 5173
    },
    backend: {
      url: 'http://localhost:3001',
      port: 3001
    },
    n8n: {
      url: 'http://localhost:5678',
      port: 5678,
      apiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU'
    },
    database: {
      host: 'localhost',
      port: 5432,
      database: 'ccjap_db',
      user: 'ccjap_admin',
      password: 'K@rur0su24'
    },
    
    // Webhooks para desarrollo local
    webhooks: {
      main: 'http://localhost:5678/webhook/whatsapp-main',
      absence: 'http://localhost:5678/webhook/process-absence',
      behavior: 'http://localhost:5678/webhook/behavior-report',
      approved: 'http://localhost:5678/webhook/send-approved-report',
      daily: 'http://localhost:5678/webhook/daily-absence-report'
    },
    
    // APIs externas
    openai: {
      apiKey: process.env.CHATGPT_API_KEY || 'sk-your-openai-api-key-here',
      model: 'gpt-4',
      maxTokens: 800,
      temperature: 0.7
    },
    
    // WhatsApp (para desarrollo usar endpoints locales)
    whatsapp: {
      useLocal: true, // Usar endpoints locales en desarrollo
      localEndpoint: 'http://localhost:3001/api/whatsapp/send-message',
      waapi: {
        url: 'https://api.waapi.app/instances/YOUR_INSTANCE/client/action/send-message',
        token: process.env.WAAPI_TOKEN || 'your-waapi-token-here'
      }
    },
    
    // Configuración de desarrollo
    debug: true,
    logLevel: 'debug'
  },
  
  production: {
    // URLs de producción (actualizar cuando se despliegue)
    frontend: {
      url: 'https://ccjap.echolab.xyz',
      port: 443
    },
    backend: {
      url: 'https://api.ccjap.echolab.xyz',
      port: 443
    },
    n8n: {
      url: 'https://n8n.echolab.xyz',
      port: 443,
      apiKey: process.env.N8N_API_KEY
    },
    database: {
      host: process.env.DB_HOST || 'postgres',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'ccjap_db',
      user: process.env.DB_USER || 'ccjap_admin',
      password: process.env.DB_PASSWORD
    },
    
    // Webhooks para producción
    webhooks: {
      main: 'https://n8n.echolab.xyz/webhook/whatsapp-main',
      absence: 'https://n8n.echolab.xyz/webhook/process-absence',
      behavior: 'https://n8n.echolab.xyz/webhook/behavior-report',
      approved: 'https://n8n.echolab.xyz/webhook/send-approved-report',
      daily: 'https://n8n.echolab.xyz/webhook/daily-absence-report'
    },
    
    // APIs externas
    openai: {
      apiKey: process.env.CHATGPT_API_KEY,
      model: 'gpt-4',
      maxTokens: 800,
      temperature: 0.7
    },
    
    // WhatsApp para producción
    whatsapp: {
      useLocal: false,
      localEndpoint: null,
      waapi: {
        url: 'https://api.waapi.app/instances/YOUR_INSTANCE/client/action/send-message',
        token: process.env.WAAPI_TOKEN
      }
    },
    
    // Configuración de producción
    debug: false,
    logLevel: 'info'
  }
};

// Exportar configuración según entorno
const currentConfig = isDevelopment ? config.development : config.production;

// Funciones auxiliares
const getWebhookUrl = (type) => {
  return currentConfig.webhooks[type] || currentConfig.webhooks.main;
};

const getWhatsAppEndpoint = () => {
  if (currentConfig.whatsapp.useLocal) {
    return currentConfig.whatsapp.localEndpoint;
  }
  return currentConfig.whatsapp.waapi.url;
};

const getDatabaseConfig = () => {
  return currentConfig.database;
};

const getN8nConfig = () => {
  return currentConfig.n8n;
};

// Validar configuración crítica
const validateConfig = () => {
  const errors = [];
  
  if (!currentConfig.openai.apiKey || currentConfig.openai.apiKey.includes('your-openai-api-key')) {
    errors.push('CHATGPT_API_KEY no está configurada correctamente');
  }
  
  if (!currentConfig.n8n.apiKey) {
    errors.push('N8N_API_KEY no está configurada');
  }
  
  if (isProduction && !currentConfig.database.password) {
    errors.push('DB_PASSWORD no está configurada para producción');
  }
  
  if (errors.length > 0) {
    console.error('❌ Errores de configuración:');
    errors.forEach(error => console.error(`   - ${error}`));
    return false;
  }
  
  return true;
};

// Mostrar configuración actual
const showCurrentConfig = () => {
  console.log(`\n🔧 Configuración actual: ${isDevelopment ? 'DESARROLLO' : 'PRODUCCIÓN'}`);
  console.log(`   Frontend: ${currentConfig.frontend.url}`);
  console.log(`   Backend: ${currentConfig.backend.url}`);
  console.log(`   n8n: ${currentConfig.n8n.url}`);
  console.log(`   Base de datos: ${currentConfig.database.host}:${currentConfig.database.port}`);
  console.log(`   WhatsApp: ${getWhatsAppEndpoint()}`);
  console.log(`   Debug: ${currentConfig.debug ? 'Activado' : 'Desactivado'}`);
};

module.exports = {
  isDevelopment,
  isProduction,
  config: currentConfig,
  getWebhookUrl,
  getWhatsAppEndpoint,
  getDatabaseConfig,
  getN8nConfig,
  validateConfig,
  showCurrentConfig
};
