const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🔍 Verificando estado de workflows en n8n...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión y obtener workflows
    const workflows = await makeN8nRequest('GET', '/workflows');
    const workflowList = workflows.data || workflows;
    
    console.log(`✅ Conexión establecida. Encontrados ${workflowList.length} workflows`);
    
    if (workflowList.length === 0) {
      console.log('⚠️ No se encontraron workflows. Ejecute los scripts de creación primero.');
      return;
    }
    
    console.log('\n📋 Estado detallado de workflows:');
    console.log('=' .repeat(80));
    
    for (const workflow of workflowList) {
      console.log(`\n🔧 Workflow: ${workflow.name}`);
      console.log(`   ID: ${workflow.id}`);
      console.log(`   Estado: ${workflow.active ? '✅ Activo' : '❌ Inactivo'}`);
      console.log(`   Nodos: ${workflow.nodes ? workflow.nodes.length : 'N/A'}`);
      console.log(`   Conexiones: ${workflow.connections ? Object.keys(workflow.connections).length : 'N/A'}`);
      
      // Buscar nodos webhook
      if (workflow.nodes) {
        const webhookNodes = workflow.nodes.filter(node => node.type === 'n8n-nodes-base.webhook');
        if (webhookNodes.length > 0) {
          console.log(`   Webhooks:`);
          webhookNodes.forEach(node => {
            const path = node.parameters?.path;
            const method = node.parameters?.httpMethod || 'POST';
            if (path) {
              console.log(`     - ${method} ${N8N_BASE_URL}/webhook/${path}`);
            }
          });
        }
        
        // Buscar nodos PostgreSQL
        const postgresNodes = workflow.nodes.filter(node => node.type === 'n8n-nodes-base.postgres');
        if (postgresNodes.length > 0) {
          console.log(`   Nodos PostgreSQL: ${postgresNodes.length}`);
          postgresNodes.forEach(node => {
            const credentialId = node.credentials?.postgres?.id;
            const credentialName = node.credentials?.postgres?.name;
            console.log(`     - Credencial: ${credentialName || 'No configurada'} (${credentialId || 'Sin ID'})`);
          });
        }
      }
    }
    
    console.log('\n' + '=' .repeat(80));
    console.log('📊 RESUMEN:');
    const activeWorkflows = workflowList.filter(w => w.active);
    console.log(`✅ Workflows activos: ${activeWorkflows.length}/${workflowList.length}`);
    
    // Verificar webhooks disponibles
    console.log('\n🔗 URLs de Webhook disponibles:');
    let webhookCount = 0;
    
    workflowList.forEach(workflow => {
      if (workflow.active && workflow.nodes) {
        const webhookNodes = workflow.nodes.filter(node => node.type === 'n8n-nodes-base.webhook');
        webhookNodes.forEach(node => {
          const path = node.parameters?.path;
          const method = node.parameters?.httpMethod || 'POST';
          if (path) {
            console.log(`   ${method} ${N8N_BASE_URL}/webhook/${path} (${workflow.name})`);
            webhookCount++;
          }
        });
      }
    });
    
    if (webhookCount === 0) {
      console.log('   ⚠️ No hay webhooks disponibles');
      console.log('\n🔧 Posibles problemas:');
      console.log('   1. Los workflows no están activos');
      console.log('   2. Faltan credenciales de PostgreSQL');
      console.log('   3. Los nodos tienen errores de configuración');
      
      console.log('\n📝 Soluciones:');
      console.log('   1. Acceder a n8n: http://localhost:5678');
      console.log('   2. Configurar credenciales de PostgreSQL manualmente');
      console.log('   3. Verificar y activar cada workflow individualmente');
    } else {
      console.log(`\n✅ ${webhookCount} webhooks disponibles para pruebas`);
    }
    
  } catch (error) {
    console.error('\n💥 Error en la verificación:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
