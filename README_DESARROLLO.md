# Sistema IA WhatsApp - Entorno de Desarrollo

> **⚠️ IMPORTANTE: CONFIGURACIÓN PARA DESARROLLO LOCAL**  
> Este proyecto está configurado para funcionar en **desarrollo local** con URLs `localhost`. 
> **NO usar en producción** sin actualizar las configuraciones correspondientes.

## 🚀 Inicio Rápido

### 1. Prerrequisitos
```bash
# Verificar instalaciones
docker --version
docker-compose --version
node --version
```

### 2. Configurar Variables de Entorno
```bash
# Crear archivo .env en la raíz del proyecto
cp .env.example .env

# Editar .env con tus credenciales
CHATGPT_API_KEY=sk-your-openai-api-key-here
N8N_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3. Ejecutar Configuración Automática
```bash
# Ejecutar script de configuración completa
node setup_sistema_ia_completo.js
```

## 🌐 Servicios Locales

| Servicio | URL Local | Puerto | Credenciales |
|----------|-----------|--------|--------------|
| Frontend | http://localhost:5173 | 5173 | - |
| Backend API | http://localhost:3001 | 3001 | - |
| n8n | http://localhost:5678 | 5678 | admin / K@rur0su24 |
| PostgreSQL | localhost:5432 | 5432 | ccjap_admin / K@rur0su24 |

## 🔗 Webhooks de Desarrollo

| Tipo | URL Local |
|------|-----------|
| Principal | http://localhost:5678/webhook/whatsapp-main |
| Ausencias | http://localhost:5678/webhook/process-absence |
| Reportes | http://localhost:5678/webhook/behavior-report |
| Aprobados | http://localhost:5678/webhook/send-approved-report |
| Diarios | http://localhost:5678/webhook/daily-absence-report |

## 🧪 Pruebas Locales

### Probar sin WhatsApp Real
```bash
# Usar curl para simular mensajes
curl -X POST http://localhost:5678/webhook/whatsapp-main \
  -H "Content-Type: application/json" \
  -d '{
    "from": "50312345678",
    "body": "Mi hijo Juan Pérez de 5to grado no asistirá hoy por enfermedad",
    "name": "María González"
  }'
```

### Probar con WhatsApp Real (Opcional)
```bash
# 1. Instalar ngrok
npm install -g ngrok

# 2. Exponer puerto local
ngrok http 5678

# 3. Usar URL de ngrok en WaAPI
# Ejemplo: https://abc123.ngrok.io/webhook/whatsapp-main
```

## 📁 Estructura del Proyecto

```
ccjapDocenteAutomatizacion/
├── config/
│   └── environment.js          # Configuración de entornos
├── backend/
│   ├── scripts/
│   │   ├── create_complete_ai_system.js    # Workflows n8n
│   │   ├── setup_complete_ai_system.js     # Configuración BD
│   │   └── create_ai_tables.sql            # Tablas de IA
│   └── routes/
│       └── ai-notifications.js             # API notificaciones
├── setup_sistema_ia_completo.js            # Script principal
├── SISTEMA_IA_WHATSAPP.md                  # Documentación completa
└── README_DESARROLLO.md                    # Esta guía
```

## 🔧 Comandos Útiles

### Docker
```bash
# Iniciar servicios
docker-compose up -d

# Ver logs
docker-compose logs -f

# Reiniciar servicios
docker-compose restart

# Detener servicios
docker-compose down
```

### Base de Datos
```bash
# Conectar a PostgreSQL
docker exec -it ccjapDocenteAutomatizacion_postgres_1 psql -U ccjap_admin -d ccjap_db

# Ejecutar migraciones
node backend/scripts/setup_complete_ai_system.js
```

### n8n
```bash
# Acceder a n8n
open http://localhost:5678

# Verificar workflows activos
curl -H "X-N8N-API-KEY: your-api-key" http://localhost:5678/api/v1/workflows
```

## 🐛 Solución de Problemas

### Error: Puerto en uso
```bash
# Verificar puertos ocupados
netstat -tulpn | grep :5678
netstat -tulpn | grep :3001
netstat -tulpn | grep :5173

# Liberar puertos
docker-compose down
```

### Error: Base de datos no conecta
```bash
# Verificar estado de PostgreSQL
docker-compose ps postgres

# Reiniciar PostgreSQL
docker-compose restart postgres
```

### Error: n8n no responde
```bash
# Verificar logs de n8n
docker-compose logs n8n

# Reiniciar n8n
docker-compose restart n8n
```

### Error: Variables de entorno
```bash
# Verificar archivo .env
cat .env

# Verificar variables cargadas
node -e "require('dotenv').config(); console.log(process.env.CHATGPT_API_KEY)"
```

## 📊 Monitoreo en Desarrollo

### Logs en Tiempo Real
```bash
# Todos los servicios
docker-compose logs -f

# Solo n8n
docker-compose logs -f n8n

# Solo backend
docker-compose logs -f backend
```

### Base de Datos
```bash
# Ver mensajes recientes
docker exec -it ccjapDocenteAutomatizacion_postgres_1 psql -U ccjap_admin -d ccjap_db -c "SELECT * FROM mensajes_whatsapp ORDER BY fecha_recepcion DESC LIMIT 10;"

# Ver reportes pendientes
docker exec -it ccjapDocenteAutomatizacion_postgres_1 psql -U ccjap_admin -d ccjap_db -c "SELECT * FROM reportes_comportamiento WHERE estado = 'pendiente_aprobacion';"
```

## 🚀 Preparar para Producción

### 1. Actualizar Configuración
```javascript
// En config/environment.js
// Cambiar NODE_ENV=production
// Actualizar URLs de producción
```

### 2. Variables de Entorno de Producción
```bash
# Configurar variables seguras
CHATGPT_API_KEY=sk-prod-key
N8N_API_KEY=prod-n8n-key
DB_PASSWORD=secure-password
```

### 3. Verificar Configuración
```bash
# Validar configuración
node -e "const config = require('./config/environment.js'); config.validateConfig();"
```

## 📞 Soporte de Desarrollo

- **Documentación Completa**: `SISTEMA_IA_WHATSAPP.md`
- **Configuración**: `config/environment.js`
- **Scripts**: `backend/scripts/`

## ⚠️ Recordatorios Importantes

1. **Nunca** usar configuración de desarrollo en producción
2. **Siempre** verificar variables de entorno antes de desplegar
3. **Mantener** credenciales seguras y no commitearlas
4. **Probar** todos los workflows antes de producción
5. **Documentar** cualquier cambio en configuración
