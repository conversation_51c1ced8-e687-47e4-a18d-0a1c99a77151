const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware, authorizeRoles } = require('../middleware/authMiddleware');

// GET /api/grupos/grados - Obtener todos los grados
router.get('/grados', authMiddleware, authorize<PERSON><PERSON>s('Director', 'Docente', 'Secretaria', 'Superadministrador', 'Director <PERSON><PERSON>'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT
        g.*,
        COUNT(a.id) as total_alumnos,
        COUNT(CASE WHEN a.estado = 'Activo' THEN 1 END) as alumnos_activos,
        u.nombre as tutor_nombre,
        COUNT(DISTINCT gw.id) as grupos_whatsapp
      FROM grados g
      LEFT JOIN alumnos a ON g.id = a.grado_id
      LEFT JOIN asignaciones_docente ad ON g.id = ad.grado_id AND ad.es_tutor = true AND ad.activo = true
      LEFT JOIN usuarios u ON ad.docente_id = u.id
      LEFT JOIN grupos_whatsapp gw ON g.id = gw.grado_id AND gw.activo = true
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' WHERE g.institucion_id = $1';
      values.push(userInstitucionId);
    }
    
    query += ' GROUP BY g.id, u.nombre ORDER BY g.nivel, g.nombre';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo grados:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/docentes - Obtener docentes disponibles
router.get('/docentes', authMiddleware, authorizeRoles('Director', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;

    let query = `
      SELECT
        u.id,
        u.nombre,
        u.email,
        u.rol,
        COUNT(DISTINCT ad.grado_id) as grados_asignados
      FROM usuarios u
      LEFT JOIN asignaciones_docente ad ON u.id = ad.docente_id AND ad.activo = true
      WHERE u.rol IN ('Docente', 'Director Academico')
    `;

    const values = [];

    if (req.user.rol !== 'Superadministrador') {
      query += ' AND u.institucion_id = $1';
      values.push(userInstitucionId);
    }

    query += ' GROUP BY u.id, u.nombre, u.email, u.rol ORDER BY u.nombre';

    const result = await db.query(query, values);

    res.json(result.rows);

  } catch (error) {
    console.error('Error obteniendo docentes:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/whatsapp - Obtener grupos de WhatsApp
router.get('/whatsapp', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT
        gw.*,
        g.nombre as grado_nombre,
        g.seccion as grado_seccion,
        g.nivel as grado_nivel,
        COUNT(mgw.id) as total_miembros
      FROM grupos_whatsapp gw
      LEFT JOIN grados g ON gw.grado_id = g.id
      LEFT JOIN miembros_grupo_whatsapp mgw ON gw.id = mgw.grupo_id AND mgw.activo = true
      WHERE gw.activo = true
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' AND gw.institucion_id = $1';
      values.push(userInstitucionId);
    }

    query += ' GROUP BY gw.id, g.nombre, g.seccion, g.nivel ORDER BY g.nivel, g.nombre, gw.nombre';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo grupos de WhatsApp:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/asignaciones - Obtener asignaciones de docentes
router.get('/asignaciones', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT 
        ad.*,
        u.nombre as docente_nombre,
        u.email as docente_email,
        u.telefono as docente_telefono,
        g.nombre as grado_nombre,
        g.nivel as grado_nivel,
        g.seccion as grado_seccion
      FROM asignaciones_docente ad
      JOIN usuarios u ON ad.docente_id = u.id
      JOIN grados g ON ad.grado_id = g.id
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' WHERE g.institucion_id = $1';
      values.push(userInstitucionId);
    }
    
    query += ' AND ad.activo = true ORDER BY g.nivel, g.nombre, ad.es_tutor DESC';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo asignaciones:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/asignaciones - Crear nueva asignación
router.post('/asignaciones', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { docente_id, grado_id, materia, es_tutor } = req.body;
    
    if (!docente_id || !grado_id) {
      return res.status(400).json({ error: 'Docente y grado son requeridos' });
    }
    
    // Si es tutor, desactivar otros tutores del mismo grado
    if (es_tutor) {
      await db.query(
        'UPDATE asignaciones_docente SET es_tutor = false WHERE grado_id = $1 AND es_tutor = true',
        [grado_id]
      );
    }
    
    const result = await db.query(`
      INSERT INTO asignaciones_docente (docente_id, grado_id, materia, es_tutor)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (docente_id, grado_id, materia) 
      DO UPDATE SET es_tutor = $4, activo = true
      RETURNING id
    `, [docente_id, grado_id, materia, es_tutor]);
    
    res.status(201).json({
      success: true,
      message: 'Asignación creada exitosamente',
      id: result.rows[0].id
    });
    
  } catch (error) {
    console.error('Error creando asignación:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// DELETE /api/grupos/asignaciones/:id - Eliminar asignación
router.delete('/asignaciones/:id', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;
    
    await db.query('UPDATE asignaciones_docente SET activo = false WHERE id = $1', [id]);
    
    res.json({
      success: true,
      message: 'Asignación eliminada exitosamente'
    });
    
  } catch (error) {
    console.error('Error eliminando asignación:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/whatsapp/:id/enviar - Enviar mensaje a grupo
router.post('/whatsapp/:id/enviar', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const { id } = req.params;
    const { mensaje, tipo_mensaje } = req.body;
    
    if (!mensaje) {
      return res.status(400).json({ error: 'Mensaje es requerido' });
    }
    
    // Obtener información del grupo
    const grupoResult = await db.query(
      'SELECT * FROM grupos_whatsapp WHERE id = $1',
      [id]
    );
    
    if (grupoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Grupo no encontrado' });
    }
    
    const grupo = grupoResult.rows[0];
    
    // Obtener miembros del grupo
    const miembrosResult = await db.query(
      'SELECT telefono, nombre FROM miembros_grupo_whatsapp WHERE grupo_id = $1 AND activo = true',
      [id]
    );
    
    // Aquí integrarías con tu API de WhatsApp para enviar el mensaje
    // Por ahora solo guardamos el registro
    
    await db.query(`
      INSERT INTO mensajes_enviados (
        telefono_destinatario,
        contenido,
        tipo,
        usuario_id,
        institucion_id,
        grupo_id
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'GRUPO_' + grupo.nombre,
      mensaje,
      tipo_mensaje || 'grupo',
      req.user.id,
      req.user.institucion_id,
      id
    ]);
    
    res.json({
      success: true,
      message: `Mensaje enviado a ${miembrosResult.rows.length} miembros del grupo ${grupo.nombre}`,
      destinatarios: miembrosResult.rows.length
    });
    
  } catch (error) {
    console.error('Error enviando mensaje a grupo:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/docentes - Obtener lista de docentes para asignaciones
router.get('/docentes', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT 
        id, 
        nombre, 
        email, 
        telefono,
        rol
      FROM usuarios 
      WHERE rol IN ('Docente', 'Director', 'Director Academico')
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' AND institucion_id = $1';
      values.push(userInstitucionId);
    }
    
    query += ' ORDER BY nombre';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo docentes:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/grados - Crear nuevo grado
router.post('/grados', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { nombre, nivel, seccion, capacidad_maxima } = req.body;
    const userInstitucionId = req.user.institucion_id;

    if (!nombre || !nivel) {
      return res.status(400).json({ error: 'Nombre y nivel son requeridos' });
    }

    const result = await db.query(`
      INSERT INTO grados (nombre, nivel, seccion, capacidad_maxima, institucion_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [nombre, nivel, seccion, capacidad_maxima || 30, userInstitucionId]);

    res.status(201).json({
      success: true,
      message: 'Grado creado exitosamente',
      grado: result.rows[0]
    });

  } catch (error) {
    console.error('Error creando grado:', error);
    if (error.code === '23505') {
      res.status(409).json({ error: 'Ya existe un grado con ese nombre y sección' });
    } else {
      res.status(500).json({ error: 'Error interno del servidor' });
    }
  }
});

// PUT /api/grupos/grados/:id - Actualizar grado
router.put('/grados/:id', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;
    const { nombre, nivel, seccion, capacidad_maxima } = req.body;

    const result = await db.query(`
      UPDATE grados
      SET nombre = $1, nivel = $2, seccion = $3, capacidad_maxima = $4
      WHERE id = $5
      RETURNING *
    `, [nombre, nivel, seccion, capacidad_maxima, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Grado no encontrado' });
    }

    res.json({
      success: true,
      message: 'Grado actualizado exitosamente',
      grado: result.rows[0]
    });

  } catch (error) {
    console.error('Error actualizando grado:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// DELETE /api/grupos/grados/:id - Eliminar grado
router.delete('/grados/:id', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;

    // Verificar si hay alumnos asignados
    const alumnosResult = await db.query('SELECT COUNT(*) FROM alumnos WHERE grado_id = $1', [id]);
    const totalAlumnos = parseInt(alumnosResult.rows[0].count);

    if (totalAlumnos > 0) {
      return res.status(400).json({
        error: `No se puede eliminar el grado porque tiene ${totalAlumnos} alumnos asignados`
      });
    }

    await db.query('UPDATE grados SET activo = false WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Grado eliminado exitosamente'
    });

  } catch (error) {
    console.error('Error eliminando grado:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/whatsapp - Crear grupo de WhatsApp
router.post('/whatsapp', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const { nombre, descripcion, tipo, grado_id } = req.body;
    const userInstitucionId = req.user.institucion_id;

    if (!nombre || !tipo) {
      return res.status(400).json({ error: 'Nombre y tipo son requeridos' });
    }

    const result = await db.query(`
      INSERT INTO grupos_whatsapp (nombre, descripcion, tipo, grado_id, institucion_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [nombre, descripcion, tipo, grado_id, userInstitucionId]);

    res.status(201).json({
      success: true,
      message: 'Grupo de WhatsApp creado exitosamente',
      grupo: result.rows[0]
    });

  } catch (error) {
    console.error('Error creando grupo WhatsApp:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/whatsapp/:id/miembros - Agregar miembro a grupo
router.post('/whatsapp/:id/miembros', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;
    const { telefono, nombre, rol } = req.body;

    if (!telefono || !nombre) {
      return res.status(400).json({ error: 'Teléfono y nombre son requeridos' });
    }

    const result = await db.query(`
      INSERT INTO miembros_grupo_whatsapp (grupo_id, telefono, nombre, rol)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (grupo_id, telefono)
      DO UPDATE SET nombre = $3, rol = $4, activo = true
      RETURNING *
    `, [id, telefono, nombre, rol || 'miembro']);

    res.status(201).json({
      success: true,
      message: 'Miembro agregado exitosamente',
      miembro: result.rows[0]
    });

  } catch (error) {
    console.error('Error agregando miembro:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/whatsapp/:id/miembros - Obtener miembros de grupo
router.get('/whatsapp/:id/miembros', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(`
      SELECT * FROM miembros_grupo_whatsapp
      WHERE grupo_id = $1 AND activo = true
      ORDER BY nombre
    `, [id]);

    res.json(result.rows);

  } catch (error) {
    console.error('Error obteniendo miembros:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/auto-assign-parents - Asignar padres automáticamente a grupos
router.post('/auto-assign-parents', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;

    // Obtener todos los grados con grupos de WhatsApp
    const gradosConGrupos = await db.query(`
      SELECT g.id as grado_id, g.nombre as grado_nombre, gw.id as grupo_id
      FROM grados g
      JOIN grupos_whatsapp gw ON g.id = gw.grado_id
      WHERE g.institucion_id = $1 AND g.activo = true AND gw.activo = true
    `, [userInstitucionId]);

    let totalAsignados = 0;

    for (const grado of gradosConGrupos.rows) {
      // Obtener alumnos del grado que tienen responsable con teléfono
      const alumnos = await db.query(`
        SELECT DISTINCT
          telefono_responsable_principal as telefono,
          nombre_responsable_principal as nombre
        FROM alumnos
        WHERE grado_id = $1
          AND telefono_responsable_principal IS NOT NULL
          AND telefono_responsable_principal != ''
          AND estado = 'Activo'
      `, [grado.grado_id]);

      // Agregar cada padre al grupo de WhatsApp
      for (const alumno of alumnos.rows) {
        try {
          await db.query(`
            INSERT INTO miembros_grupo_whatsapp (grupo_id, telefono, nombre, rol)
            VALUES ($1, $2, $3, 'miembro')
            ON CONFLICT (grupo_id, telefono) DO NOTHING
          `, [grado.grupo_id, alumno.telefono, alumno.nombre]);
          totalAsignados++;
        } catch (err) {
          console.error('Error asignando padre:', err);
        }
      }
    }

    res.json({
      success: true,
      message: `${totalAsignados} padres asignados automáticamente a grupos de WhatsApp`,
      totalAsignados
    });

  } catch (error) {
    console.error('Error en asignación automática:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

module.exports = router;
