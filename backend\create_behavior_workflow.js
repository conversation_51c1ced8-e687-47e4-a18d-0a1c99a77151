const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🚀 Creando workflow de reportes de comportamiento...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow para reportes de comportamiento
async function createBehaviorWorkflow() {
  console.log('\n📋 Creando workflow de reportes de comportamiento...');
  
  const workflowData = {
    name: 'WhatsApp IA - Reportes de Comportamiento',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir reportes
      {
        parameters: {
          httpMethod: 'POST',
          path: 'behavior-report',
          responseMode: 'responseNode'
        },
        id: 'behavior-webhook',
        name: 'Trigger Reporte',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Crear reporte en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              tipo_reporte: 'comportamiento',
              descripcion: '={{ $json.description }}',
              evidencia_urls: '={{ JSON.stringify($json.evidence_urls || []) }}',
              severidad: '={{ $json.severity || "medium" }}',
              reportado_por_telefono: '={{ $json.sender_phone }}',
              reportado_por_nombre: '={{ $json.sender_name }}',
              estudiante_nombre: '={{ $json.student_name }}',
              grado: '={{ $json.grade }}',
              requiere_aprobacion: true,
              estado: 'pendiente_aprobacion',
              fecha_reporte: '={{ new Date().toISOString() }}'
            }
          },
          table: 'reportes_comportamiento'
        },
        id: 'create-report',
        name: 'Crear Reporte',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 3. Notificar al director para aprobación
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              tipo_notificacion: 'behavior_report_approval',
              titulo: 'Nuevo Reporte de Comportamiento',
              mensaje: 'Se ha recibido un reporte de comportamiento que requiere aprobación: {{ $("Trigger Reporte").first().json.student_name }}',
              destinatario_id: 1,
              prioridad: '={{ $("Trigger Reporte").first().json.severity || "normal" }}',
              accion_requerida: true,
              url_accion: '/dashboard/reports/{{ $("Crear Reporte").first().json.id }}',
              datos_adicionales: '={{ JSON.stringify($json) }}'
            }
          },
          table: 'notificaciones_dashboard'
        },
        id: 'notify-director',
        name: 'Notificar Director',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [680, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 4. Preparar confirmación de recepción
      {
        parameters: {
          jsCode: `
// Preparar confirmación de recepción
const reportData = $('Trigger Reporte').first().json;

const mensaje = \`📋 *Reporte Recibido*

Hemos recibido su reporte sobre el comportamiento de *\${reportData.student_name}*.

⏳ El director revisará la información y evidencia proporcionada.

📞 Nos pondremos en contacto con usted una vez que se haya procesado el reporte.

¡Gracias por mantenernos informados!\`;

return [{
  json: {
    phone: reportData.sender_phone,
    message: mensaje,
    student_name: reportData.student_name,
    report_id: $('Crear Reporte').first().json.id,
    status: 'received'
  }
}];
`
        },
        id: 'prepare-receipt',
        name: 'Confirmar Recepción',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 300]
      },
      
      // 5. Respuesta del webhook
      {
        parameters: {
          options: {}
        },
        id: 'webhook-response',
        name: 'Respuesta Webhook',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [1120, 300]
      }
    ],
    
    connections: {
      'Trigger Reporte': {
        main: [
          [
            {
              node: 'Crear Reporte',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Crear Reporte': {
        main: [
          [
            {
              node: 'Notificar Director',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Notificar Director': {
        main: [
          [
            {
              node: 'Confirmar Recepción',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Confirmar Recepción': {
        main: [
          [
            {
              node: 'Respuesta Webhook',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow de reportes creado con ID: ${result.id}`);
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow de reportes:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow de reportes
    const behaviorWorkflowId = await createBehaviorWorkflow();
    
    console.log('\n🎉 ¡Workflow de reportes creado exitosamente!');
    console.log('📋 Resumen:');
    console.log(`   - Workflow Reportes: ${behaviorWorkflowId}`);
    console.log('\n🔗 URLs de Webhook:');
    console.log(`   - Reportes: ${N8N_BASE_URL}/webhook/behavior-report`);
    console.log('\n📝 Próximos pasos:');
    console.log('   1. Acceder a n8n: http://localhost:5678');
    console.log('   2. Verificar que el workflow esté activo');
    console.log('   3. Configurar credenciales de PostgreSQL si es necesario');
    console.log('   4. Probar enviando datos de reporte al webhook');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
