import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import useAuthState from '../hooks/useAuthState';

/**
 * Componente para proteger rutas basado en autenticación y permisos
 * @param {Object} props - Propiedades del componente
 * @param {React.ReactNode} props.children - Componentes hijos a renderizar si tiene acceso
 * @param {string[]} props.requiredRoles - Roles requeridos para acceder
 * @param {string[]} props.requiredPermissions - Permisos requeridos para acceder
 * @param {string} props.redirectTo - Ruta a la que redirigir si no tiene acceso
 * @param {React.ReactNode} props.fallback - Componente a mostrar si no tiene acceso
 */
const ProtectedRoute = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  redirectTo = '/login',
  fallback = null
}) => {
  const { isAuthenticated, user, isSuperAdmin, hasPermission } = useAuth();

  console.log('🔒 ProtectedRoute - Estado:', {
    isAuthenticated,
    user: user?.nombre,
    rol: user?.rol,
    isSuperAdmin: isSuperAdmin(),
    requiredRoles,
    requiredPermissions
  });

  // Si no está autenticado, redirigir al login
  if (!isAuthenticated) {
    console.log('❌ No autenticado, redirigiendo a login');
    return <Navigate to={redirectTo} replace />;
  }

  // Si es superadministrador, permitir acceso a todo
  if (isSuperAdmin()) {
    console.log('✅ Superadministrador detectado, acceso completo permitido');
    return children;
  }

  // Función para verificar roles
  const hasRole = (role) => {
    return user?.rol === role;
  };

  // Verificar roles requeridos
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => hasRole(role));
    if (!hasRequiredRole) {
      console.log('❌ Rol requerido no encontrado:', { userRole: user?.rol, requiredRoles });
      return fallback || <Navigate to="/unauthorized" replace />;
    }
  }

  // Verificar permisos requeridos
  if (requiredPermissions.length > 0) {
    const hasRequiredPermission = requiredPermissions.some(permission => hasPermission(permission));
    if (!hasRequiredPermission) {
      console.log('❌ Permiso requerido no encontrado:', { userRole: user?.rol, requiredPermissions });
      return fallback || <Navigate to="/unauthorized" replace />;
    }
  }

  console.log('✅ Acceso permitido');
  // Si pasa todas las verificaciones, renderizar los hijos
  return children;
};

/**
 * Componente para mostrar cuando el usuario no tiene permisos
 */
export const UnauthorizedPage = () => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
        <div className="text-6xl mb-4">🔒</div>
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Acceso No Autorizado
        </h1>
        <p className="text-gray-600 mb-6">
          No tienes permisos para acceder a esta página.
        </p>
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <p className="text-sm text-gray-700">
            <strong>Usuario:</strong> {user?.nombre || 'No identificado'}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Rol:</strong> {user?.rol || 'Sin rol'}
          </p>
        </div>
        <div className="space-y-3">
          <button
            onClick={() => window.history.back()}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Volver Atrás
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
          >
            Ir al Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Hook para verificar permisos en componentes
 */
export const usePermissions = () => {
  const { user, isSuperAdmin, hasPermission } = useAuth();

  // Función para verificar roles
  const hasRole = (role) => {
    return user?.rol === role;
  };

  // Función para verificar si puede acceder a una ruta
  const canAccessRoute = (route) => {
    if (!user) return false;
    if (isSuperAdmin()) return true;

    const routePermissions = {
      '/configuracion-avanzada': ['admin', 'superadmin'],
      '/usuarios': ['admin', 'director', 'superadmin'],
      '/gestion-academica': ['admin', 'director', 'coordinador', 'superadmin'],
      '/dashboard-docente': ['docente', 'admin', 'superadmin']
    };

    const requiredPermissions = routePermissions[route];
    if (!requiredPermissions) return true;

    return requiredPermissions.some(perm => hasPermission(perm));
  };

  return {
    user,
    hasRole,
    hasPermission,
    isSuperAdmin,
    canAccessRoute,
    // Funciones de conveniencia
    canManageUsers: () => isSuperAdmin() || hasRole('Director') || hasPermission('admin'),
    canManageAcademics: () => isSuperAdmin() || hasRole('Director') || hasRole('Coordinador Académico'),
    canViewReports: () => isSuperAdmin() || hasRole('Director') || hasRole('Coordinador Académico'),
    canManageSystem: () => isSuperAdmin() || hasPermission('admin'),
    canAccessAdvancedConfig: () => isSuperAdmin() || hasPermission('admin')
  };
};

export default ProtectedRoute;
