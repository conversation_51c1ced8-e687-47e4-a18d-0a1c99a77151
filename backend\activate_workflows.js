const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🔄 Activando workflows de IA en n8n...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Función para obtener todos los workflows
async function getWorkflows() {
  console.log('📋 Obteniendo lista de workflows...');
  const workflows = await makeN8nRequest('GET', '/workflows');
  return workflows.data || workflows;
}

// Función para activar un workflow
async function activateWorkflow(workflowId, workflowName) {
  try {
    console.log(`🔄 Activando workflow: ${workflowName} (${workflowId})`);

    // Usar el endpoint específico para activar/desactivar
    await makeN8nRequest('POST', `/workflows/${workflowId}/activate`);
    console.log(`✅ Workflow activado: ${workflowName}`);
    return true;
  } catch (error) {
    console.error(`❌ Error activando workflow ${workflowName}:`, error.message);
    return false;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Obtener todos los workflows
    const workflows = await getWorkflows();
    console.log(`📊 Encontrados ${workflows.length} workflows`);
    
    if (workflows.length === 0) {
      console.log('⚠️  No se encontraron workflows. Ejecute primero los scripts de creación.');
      return;
    }
    
    // Mostrar workflows encontrados
    console.log('\n📋 Workflows encontrados:');
    workflows.forEach((workflow, index) => {
      const status = workflow.active ? '✅ Activo' : '❌ Inactivo';
      console.log(`   ${index + 1}. ${workflow.name} - ${status} (ID: ${workflow.id})`);
    });
    
    // Activar todos los workflows inactivos
    console.log('\n🔄 Activando workflows...');
    let activatedCount = 0;
    
    for (const workflow of workflows) {
      if (!workflow.active) {
        const success = await activateWorkflow(workflow.id, workflow.name);
        if (success) {
          activatedCount++;
        }
      } else {
        console.log(`✅ Ya activo: ${workflow.name}`);
      }
    }
    
    console.log('\n🎉 ¡Proceso completado!');
    console.log(`📊 Workflows activados: ${activatedCount}`);
    console.log(`📊 Total de workflows: ${workflows.length}`);
    
    // Verificar estado final
    console.log('\n🔍 Verificando estado final...');
    const updatedWorkflows = await getWorkflows();
    const activeWorkflows = updatedWorkflows.filter(w => w.active);
    
    console.log(`✅ Workflows activos: ${activeWorkflows.length}/${updatedWorkflows.length}`);
    
    if (activeWorkflows.length === updatedWorkflows.length) {
      console.log('\n🎊 ¡Todos los workflows están activos!');
      console.log('\n🔗 URLs de Webhook disponibles:');
      
      activeWorkflows.forEach(workflow => {
        // Buscar nodos webhook en el workflow
        if (workflow.nodes) {
          const webhookNodes = workflow.nodes.filter(node => node.type === 'n8n-nodes-base.webhook');
          webhookNodes.forEach(node => {
            const path = node.parameters?.path;
            if (path) {
              console.log(`   - ${workflow.name}: ${N8N_BASE_URL}/webhook/${path}`);
            }
          });
        }
      });
      
      console.log('\n📝 Próximos pasos:');
      console.log('1. Ejecutar pruebas: node test_ai_system.js');
      console.log('2. Configurar credenciales de PostgreSQL en n8n si es necesario');
      console.log('3. Probar enviando mensajes a los webhooks');
      
    } else {
      console.log('\n⚠️  Algunos workflows no se pudieron activar');
      console.log('🔧 Revise la configuración en n8n manualmente');
    }
    
  } catch (error) {
    console.error('\n💥 Error en la activación:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
