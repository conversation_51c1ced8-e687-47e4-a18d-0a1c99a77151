const axios = require('axios');

// URLs de los webhooks locales
const WEBHOOKS = {
  main: 'http://localhost:5678/webhook/whatsapp-main',
  absence: 'http://localhost:5678/webhook/process-absence',
  behavior: 'http://localhost:5678/webhook/behavior-report'
};

console.log('🧪 Probando Sistema de IA WhatsApp');
console.log('=' .repeat(50));

// Función para hacer peticiones
async function testWebhook(url, data, description) {
  try {
    console.log(`\n📡 Probando: ${description}`);
    console.log(`🔗 URL: ${url}`);
    console.log(`📤 Datos:`, JSON.stringify(data, null, 2));
    
    const response = await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log(`✅ Respuesta exitosa (${response.status})`);
    console.log(`📥 Respuesta:`, JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response) {
      console.log(`📥 Respuesta de error:`, JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

// Función principal de pruebas
async function runTests() {
  console.log('\n🚀 Iniciando pruebas del sistema...\n');
  
  let successCount = 0;
  let totalTests = 0;
  
  // Test 1: Mensaje de ausencia
  totalTests++;
  console.log(`\n📋 TEST 1: Reporte de Ausencia`);
  const absenceMessage = {
    from: '50312345678',
    body: 'Buenos días, mi hijo Juan Pérez de 5to grado no asistirá hoy por enfermedad',
    name: 'María González'
  };
  
  if (await testWebhook(WEBHOOKS.main, absenceMessage, 'Análisis de mensaje de ausencia')) {
    successCount++;
  }
  
  // Test 2: Consulta general
  totalTests++;
  console.log(`\n📋 TEST 2: Consulta General`);
  const consultaMessage = {
    from: '50387654321',
    body: '¿A qué hora salen los estudiantes de 3er grado?',
    name: 'Carlos Rodríguez'
  };
  
  if (await testWebhook(WEBHOOKS.main, consultaMessage, 'Análisis de consulta general')) {
    successCount++;
  }
  
  // Test 3: Reporte de comportamiento
  totalTests++;
  console.log(`\n📋 TEST 3: Reporte de Comportamiento`);
  const behaviorMessage = {
    from: '50398765432',
    body: 'Quiero reportar un problema de comportamiento de mi hija Ana López en el recreo',
    name: 'Patricia López'
  };
  
  if (await testWebhook(WEBHOOKS.main, behaviorMessage, 'Análisis de reporte de comportamiento')) {
    successCount++;
  }
  
  // Test 4: Procesamiento directo de ausencia
  totalTests++;
  console.log(`\n📋 TEST 4: Procesamiento Directo de Ausencia`);
  const directAbsence = {
    student_name: 'Juan Pérez',
    grade: '5to',
    reason: 'Enfermedad',
    sender_phone: '50312345678',
    sender_name: 'María González'
  };
  
  if (await testWebhook(WEBHOOKS.absence, directAbsence, 'Procesamiento directo de ausencia')) {
    successCount++;
  }
  
  // Test 5: Reporte directo de comportamiento
  totalTests++;
  console.log(`\n📋 TEST 5: Reporte Directo de Comportamiento`);
  const directBehavior = {
    student_name: 'Ana López',
    grade: '3ro',
    description: 'Problema de comportamiento en el recreo',
    severity: 'medium',
    sender_phone: '50398765432',
    sender_name: 'Patricia López',
    evidence_urls: ['https://example.com/photo1.jpg']
  };
  
  if (await testWebhook(WEBHOOKS.behavior, directBehavior, 'Reporte directo de comportamiento')) {
    successCount++;
  }
  
  // Resumen de pruebas
  console.log('\n' + '=' .repeat(50));
  console.log('📊 RESUMEN DE PRUEBAS');
  console.log('=' .repeat(50));
  console.log(`✅ Pruebas exitosas: ${successCount}/${totalTests}`);
  console.log(`❌ Pruebas fallidas: ${totalTests - successCount}/${totalTests}`);
  console.log(`📈 Porcentaje de éxito: ${Math.round((successCount / totalTests) * 100)}%`);
  
  if (successCount === totalTests) {
    console.log('\n🎉 ¡Todas las pruebas pasaron exitosamente!');
    console.log('✅ El sistema de IA WhatsApp está funcionando correctamente');
  } else {
    console.log('\n⚠️  Algunas pruebas fallaron');
    console.log('🔧 Revise la configuración de n8n y las credenciales');
  }
  
  console.log('\n📝 Próximos pasos:');
  console.log('1. Acceder a n8n: http://localhost:5678');
  console.log('2. Verificar que todos los workflows estén activos');
  console.log('3. Configurar credenciales de PostgreSQL si es necesario');
  console.log('4. Revisar los logs de ejecución en n8n');
  console.log('5. Verificar los datos en la base de datos');
  
  console.log('\n🔍 Para verificar datos en la base de datos:');
  console.log('docker exec -it ccjapdocenteautomatizacion-postgres-1 psql -U ccjapuser -d ccjapdb');
  console.log('SELECT * FROM mensajes_whatsapp ORDER BY fecha_recepcion DESC LIMIT 5;');
  console.log('SELECT * FROM ausencias ORDER BY fecha_ausencia DESC LIMIT 5;');
  console.log('SELECT * FROM reportes_comportamiento ORDER BY fecha_reporte DESC LIMIT 5;');
  console.log('SELECT * FROM notificaciones_dashboard ORDER BY fecha_creacion DESC LIMIT 5;');
}

// Función para verificar servicios
async function checkServices() {
  console.log('🔍 Verificando servicios...');
  
  const services = [
    { name: 'n8n', url: 'http://localhost:5678' },
    { name: 'Backend', url: 'http://localhost:3001' },
    { name: 'Frontend', url: 'http://localhost:5173' }
  ];
  
  for (const service of services) {
    try {
      await axios.get(service.url, { timeout: 5000 });
      console.log(`✅ ${service.name}: Funcionando`);
    } catch (error) {
      console.log(`❌ ${service.name}: No disponible`);
    }
  }
}

// Ejecutar verificaciones y pruebas
async function main() {
  try {
    await checkServices();
    await runTests();
  } catch (error) {
    console.error('\n💥 Error general:', error.message);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  main();
}

module.exports = { runTests, checkServices };
