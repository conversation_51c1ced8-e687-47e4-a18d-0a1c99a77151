const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware } = require('../middleware/authMiddleware');

// Enviar notificaciones automáticas
router.post('/enviar', async (req, res) => {
  try {
    const {
      tipo,
      mensaje,
      telefono,
      prioridad,
      grado,
      seccion,
      motivo,
      requiere_atencion
    } = req.body;

    // Insertar notificación en la base de datos
    const query = `
      INSERT INTO notificaciones 
      (tipo, mensaje, telefono, prioridad, grado, seccion, motivo, requiere_atencion, fecha_creacion, institucion_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), $9)
      RETURNING *
    `;

    const values = [
      tipo,
      mensaje,
      telefono || null,
      prioridad || 'normal',
      grado || null,
      seccion || null,
      motivo || null,
      requiere_atencion || false,
      1 // institucion_id por defecto
    ];

    const result = await db.query(query, values);

    // Determinar destinatarios según el tipo
    let destinatarios = [];

    if (tipo === 'director') {
      // Buscar usuarios con rol de director
      const directorQuery = `
        SELECT email, nombre FROM usuarios
        WHERE rol = 'Director' AND institucion_id = $1
      `;
      const directores = await db.query(directorQuery, [1]);
      destinatarios = directores.rows;
    } else if (tipo === 'docente' && grado && seccion) {
      // Buscar docentes asignados al grado y sección
      const docenteQuery = `
        SELECT u.email, u.nombre FROM usuarios u
        JOIN asignaciones_docentes ad ON u.id = ad.docente_id
        WHERE ad.grado = $1 AND ad.seccion = $2 AND u.institucion_id = $3
      `;
      const docentes = await db.query(docenteQuery, [grado, seccion, 1]);
      destinatarios = docentes.rows;
    } else if (tipo === 'administracion') {
      // Buscar personal administrativo
      const adminQuery = `
        SELECT email, nombre FROM usuarios
        WHERE rol IN ('Secretario', 'Coordinador Académico') AND institucion_id = $1
      `;
      const admins = await db.query(adminQuery, [1]);
      destinatarios = admins.rows;
    }

    console.log(`📢 Notificación ${tipo} creada: ${mensaje.substring(0, 50)}...`);
    console.log(`👥 Destinatarios: ${destinatarios.length}`);
    
    res.json({
      success: true,
      message: 'Notificación enviada exitosamente',
      notificacion: result.rows[0],
      destinatarios: destinatarios.length
    });

  } catch (error) {
    console.error('Error enviando notificación:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// Obtener notificaciones pendientes
router.get('/pendientes', authMiddleware, async (req, res) => {
  try {
    const query = `
      SELECT * FROM notificaciones 
      WHERE institucion_id = $1 AND leida = false
      ORDER BY fecha_creacion DESC
      LIMIT 50
    `;

    const result = await db.query(query, [req.user.institucion_id]);
    
    res.json({
      success: true,
      notificaciones: result.rows
    });

  } catch (error) {
    console.error('Error obteniendo notificaciones:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor'
    });
  }
});

// Marcar notificación como leída
router.put('/:id/leer', authMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    
    const query = `
      UPDATE notificaciones 
      SET leida = true, fecha_lectura = NOW()
      WHERE id = $1 AND institucion_id = $2
      RETURNING *
    `;

    const result = await db.query(query, [id, req.user.institucion_id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Notificación no encontrada'
      });
    }

    res.json({
      success: true,
      message: 'Notificación marcada como leída',
      notificacion: result.rows[0]
    });

  } catch (error) {
    console.error('Error marcando notificación como leída:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor'
    });
  }
});

// Obtener estadísticas de notificaciones
router.get('/estadisticas', authMiddleware, async (req, res) => {
  try {
    const query = `
      SELECT 
        tipo,
        COUNT(*) as total,
        COUNT(CASE WHEN leida = false THEN 1 END) as pendientes,
        COUNT(CASE WHEN prioridad = 'alta' THEN 1 END) as alta_prioridad
      FROM notificaciones 
      WHERE institucion_id = $1 
        AND fecha_creacion >= CURRENT_DATE - INTERVAL '7 days'
      GROUP BY tipo
      ORDER BY total DESC
    `;

    const result = await db.query(query, [req.user.institucion_id]);
    
    res.json({
      success: true,
      estadisticas: result.rows
    });

  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor'
    });
  }
});

module.exports = router;
