const axios = require('axios');

// Configuración
const N8N_BASE_URL = process.env.N8N_URL || 'http://localhost:5678';
const N8N_API_KEY = process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';
const BACKEND_URL = process.env.BACKEND_URL || 'http://backend:3001';

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// 1. Workflow Principal: Análisis de Mensajes con ChatGPT
async function createMainAIWorkflow() {
  console.log("Creando workflow principal de análisis con IA...");
  
  const workflowData = {
    name: 'Sistema IA WhatsApp - Análisis Principal',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // Webhook para recibir mensajes
      {
        parameters: {
          httpMethod: 'POST',
          path: 'whatsapp-main',
          responseMode: 'responseNode'
        },
        id: 'webhook-main',
        name: 'WhatsApp Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // Guardar mensaje inicial
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.from }}',
              texto_mensaje: '={{ $json.body }}',
              fecha_recepcion: '={{ new Date().toISOString() }}',
              institucion_id: 1,
              procesado: false,
              nombre_remitente: '={{ $json.name || $json.from }}',
              tipo_mensaje: 'pendiente'
            }
          },
          table: 'mensajes_whatsapp'
        },
        id: 'save-message',
        name: 'Guardar Mensaje',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },
      
      // Obtener historial del usuario
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $("WhatsApp Webhook").first().json.from }}'
            }
          },
          table: 'mensajes_whatsapp',
          limit: 10,
          sort: {
            values: [
              {
                column: 'fecha_recepcion',
                direction: 'DESC'
              }
            ]
          }
        },
        id: 'get-history',
        name: 'Obtener Historial',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [680, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },
      
      // Obtener información de estudiantes relacionados
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_responsable_principal: '={{ $("WhatsApp Webhook").first().json.from }}'
            }
          },
          table: 'alumnos',
          limit: 5
        },
        id: 'get-students',
        name: 'Obtener Estudiantes',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [680, 500],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // Preparar contexto para ChatGPT
      {
        parameters: {
          jsCode: `
// Preparar contexto completo para ChatGPT
const currentMessage = $input.first().json.body;
const senderPhone = $input.first().json.from;
const senderName = $input.first().json.name || senderPhone;

// Obtener historial
const history = $('Obtener Historial').all().map(item => ({
  message: item.json.texto_mensaje,
  date: item.json.fecha_recepcion,
  type: item.json.tipo_mensaje
}));

// Obtener estudiantes asociados
const students = $('Obtener Estudiantes').all().map(item => ({
  name: item.json.nombre_completo,
  grade: item.json.grado_actual,
  section: item.json.seccion
}));

// Crear contexto de memoria
const memoryContext = history.length > 0
  ? "Historial reciente:\\n" + history.slice(0, 3).map((h, i) =>
      \`\${i + 1}. [\${new Date(h.date).toLocaleDateString()}] \${h.message}\`
    ).join("\\n")
  : "Primera conversación.";

// Información de estudiantes
const studentsContext = students.length > 0
  ? "Estudiantes asociados a este número:\\n" + students.map(s =>
      \`- \${s.name} (\${s.grade} \${s.section})\`
    ).join("\\n")
  : "No hay estudiantes registrados con este número.";

// Sistema prompt mejorado
const systemPrompt = \`Eres el asistente inteligente del Colegio Cristiano Jerusalén de los Altos de Palencia (CCJAP).

INFORMACIÓN DEL COLEGIO:
- Horario: 7:00 AM - 12:00 PM (Lunes a Viernes)
- Director disponible: 8:00 AM - 2:00 PM
- Teléfono: +503 1234-5678

CAPACIDADES PRINCIPALES:
1. AUSENCIAS: Procesar reportes de inasistencia
2. CONSULTAS: Responder sobre horarios, actividades, tareas
3. REPORTES: Gestionar reportes de comportamiento con evidencia
4. ESCALAMIENTO: Notificar al director cuando sea necesario

TIPOS DE MENSAJE A IDENTIFICAR:
- "ausencia": Reporte de inasistencia estudiantil
- "consulta": Pregunta sobre información general
- "reporte_comportamiento": Reporte con evidencia (fotos/videos)
- "director_atencion": Requiere intervención del director
- "general": Mensaje general

\${studentsContext}

\${memoryContext}

MENSAJE ACTUAL: "\${currentMessage}"
REMITENTE: \${senderName} (\${senderPhone})

Analiza el mensaje y clasifica correctamente. Responde de forma profesional y empática.\`;

return [{
  json: {
    system_prompt: systemPrompt,
    user_message: currentMessage,
    sender_phone: senderPhone,
    sender_name: senderName,
    students: students,
    history: history,
    message_id: $('Guardar Mensaje').first().json.id
  }
}];
`
        },
        id: 'prepare-context',
        name: 'Preparar Contexto IA',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 300]
      },

      // Llamada a ChatGPT con funciones
      {
        parameters: {
          url: 'https://api.openai.com/v1/chat/completions',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'openAiApi',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'model',
                value: 'gpt-4'
              },
              {
                name: 'messages',
                value: '=[{"role": "system", "content": "{{ $json.system_prompt }}"}, {"role": "user", "content": "{{ $json.user_message }}"}]'
              },
              {
                name: 'max_tokens',
                value: 800
              },
              {
                name: 'temperature',
                value: 0.7
              },
              {
                name: 'functions',
                value: '=[{"name": "classify_and_respond", "description": "Classify message and provide appropriate response", "parameters": {"type": "object", "properties": {"message_type": {"type": "string", "enum": ["ausencia", "consulta", "reporte_comportamiento", "director_atencion", "general"]}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "extracted_data": {"type": "object", "properties": {"student_name": {"type": "string"}, "grade": {"type": "string"}, "reason": {"type": "string"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "urgent"]}}}, "requires_director_approval": {"type": "boolean"}, "auto_send": {"type": "boolean"}, "suggested_response": {"type": "string"}, "next_action": {"type": "string"}}, "required": ["message_type", "confidence", "suggested_response", "auto_send"]}}]'
              },
              {
                name: 'function_call',
                value: '{"name": "classify_and_respond"}'
              }
            ]
          }
        },
        id: 'chatgpt-analysis',
        name: 'Análisis ChatGPT',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [1120, 300],
        credentials: {
          openAiApi: {
            id: 'openai-ccjap',
            name: 'OpenAI CCJAP'
          }
        }
      }
    ],
    
      // Procesar respuesta de ChatGPT
      {
        parameters: {
          jsCode: `
// Procesar y estructurar respuesta de ChatGPT
const chatgptResponse = $input.first().json;
const originalData = $('Preparar Contexto IA').first().json;

let analysis = {
  message_type: 'general',
  confidence: 0.5,
  suggested_response: 'Gracias por contactarnos. Un miembro del equipo le responderá pronto.',
  auto_send: false,
  requires_director_approval: false,
  extracted_data: {},
  severity: 'low'
};

try {
  if (chatgptResponse.choices && chatgptResponse.choices[0]) {
    const choice = chatgptResponse.choices[0];

    if (choice.function_call && choice.function_call.arguments) {
      const functionArgs = JSON.parse(choice.function_call.arguments);
      analysis = { ...analysis, ...functionArgs };
    } else if (choice.message && choice.message.content) {
      analysis.suggested_response = choice.message.content;
    }
  }
} catch (error) {
  console.error('Error procesando ChatGPT:', error);
}

// Determinar acciones automáticas
if (analysis.message_type === 'ausencia' && analysis.confidence > 0.8) {
  analysis.auto_send = true;
  analysis.requires_director_approval = false;
} else if (analysis.message_type === 'reporte_comportamiento') {
  analysis.auto_send = false;
  analysis.requires_director_approval = true;
} else if (analysis.message_type === 'director_atencion') {
  analysis.auto_send = false;
  analysis.requires_director_approval = true;
}

return [{
  json: {
    ...analysis,
    original_message: originalData.user_message,
    sender_phone: originalData.sender_phone,
    sender_name: originalData.sender_name,
    students: originalData.students,
    message_id: originalData.message_id,
    timestamp: new Date().toISOString(),
    processed_by: 'chatgpt-ai'
  }
}];
`
        },
        id: 'process-response',
        name: 'Procesar Respuesta IA',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1340, 300]
      },

      // Actualizar mensaje con análisis
      {
        parameters: {
          operation: 'update',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              ai_analysis: '={{ JSON.stringify($json) }}',
              tipo_mensaje: '={{ $json.message_type }}',
              procesado: true,
              confidence_score: '={{ $json.confidence }}',
              requires_approval: '={{ $json.requires_director_approval }}'
            }
          },
          table: 'mensajes_whatsapp',
          updateKey: 'id',
          columnToMatchOn: 'id',
          valueToMatchOn: '={{ $json.message_id }}'
        },
        id: 'update-message',
        name: 'Actualizar Mensaje',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1560, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      }
    ],

    connections: {
      'WhatsApp Webhook': {
        main: [
          [
            {
              node: 'Guardar Mensaje',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Guardar Mensaje': {
        main: [
          [
            {
              node: 'Obtener Historial',
              type: 'main',
              index: 0
            },
            {
              node: 'Obtener Estudiantes',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Obtener Historial': {
        main: [
          [
            {
              node: 'Preparar Contexto IA',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Obtener Estudiantes': {
        main: [
          [
            {
              node: 'Preparar Contexto IA',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Contexto IA': {
        main: [
          [
            {
              node: 'Análisis ChatGPT',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Análisis ChatGPT': {
        main: [
          [
            {
              node: 'Procesar Respuesta IA',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar Respuesta IA': {
        main: [
          [
            {
              node: 'Actualizar Mensaje',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    },
    
    active: true,
    tags: ['whatsapp', 'ia', 'principal']
  };
  
  const result = await makeN8nRequest('POST', '/workflows', workflowData);
  console.log(`Workflow principal creado con ID: ${result.id}`);
  return result.id;
}

// 2. Workflow de Gestión de Ausencias Automáticas
async function createAbsenceWorkflow() {
  console.log("Creando workflow de gestión de ausencias...");

  const workflowData = {
    name: 'Sistema IA - Gestión de Ausencias',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // Trigger por webhook interno
      {
        parameters: {
          httpMethod: 'POST',
          path: 'process-absence',
          responseMode: 'responseNode'
        },
        id: 'absence-webhook',
        name: 'Trigger Ausencia',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },

      // Buscar estudiante por nombre y grado
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              nombre_completo: '={{ $json.student_name }}',
              grado_actual: '={{ $json.grade }}'
            }
          },
          table: 'alumnos',
          limit: 1
        },
        id: 'find-student',
        name: 'Buscar Estudiante',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // Crear registro de ausencia
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              alumno_id: '={{ $("Buscar Estudiante").first().json.id }}',
              fecha_ausencia: '={{ new Date().toISOString().split("T")[0] }}',
              motivo: '={{ $("Trigger Ausencia").first().json.reason }}',
              justificado: true,
              reportado_por_telefono: '={{ $("Trigger Ausencia").first().json.sender_phone }}',
              reportado_por_nombre: '={{ $("Trigger Ausencia").first().json.sender_name }}',
              notificado_docente: false,
              confirmado_recibido: false
            }
          },
          table: 'ausencias'
        },
        id: 'create-absence',
        name: 'Crear Ausencia',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [680, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // Notificar al docente
      {
        parameters: {
          url: `${BACKEND_URL}/api/notifications/teacher`,
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'type',
                value: 'student_absence'
              },
              {
                name: 'student_id',
                value: '={{ $("Buscar Estudiante").first().json.id }}'
              },
              {
                name: 'absence_id',
                value: '={{ $("Crear Ausencia").first().json.id }}'
              },
              {
                name: 'message',
                value: 'Ausencia reportada por padre de familia vía WhatsApp'
              }
            ]
          }
        },
        id: 'notify-teacher',
        name: 'Notificar Docente',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [900, 300]
      },

      // Enviar confirmación al padre
      {
        parameters: {
          url: 'https://api.waapi.app/instances/YOUR_INSTANCE/client/action/send-message',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              },
              {
                name: 'Authorization',
                value: 'Bearer YOUR_WAAPI_TOKEN'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'chatId',
                value: '={{ $("Trigger Ausencia").first().json.sender_phone }}@c.us'
              },
              {
                name: 'message',
                value: '✅ *Ausencia Registrada*\n\nHemos registrado la ausencia de *{{ $("Buscar Estudiante").first().json.nombre_completo }}* para el día de hoy.\n\n📋 El docente ha sido notificado automáticamente.\n\n¡Que se mejore pronto! 🙏'
              }
            ]
          }
        },
        id: 'send-confirmation',
        name: 'Enviar Confirmación',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [1120, 300]
      }
    ],

    connections: {
      'Trigger Ausencia': {
        main: [
          [
            {
              node: 'Buscar Estudiante',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Buscar Estudiante': {
        main: [
          [
            {
              node: 'Crear Ausencia',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Crear Ausencia': {
        main: [
          [
            {
              node: 'Notificar Docente',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Notificar Docente': {
        main: [
          [
            {
              node: 'Enviar Confirmación',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    },

    active: true,
    tags: ['ausencias', 'automatico']
  };

  const result = await makeN8nRequest('POST', '/workflows', workflowData);
  console.log(`Workflow de ausencias creado con ID: ${result.id}`);
  return result.id;
}

// Función principal para crear todos los workflows
async function createCompleteAISystem() {
  try {
    console.log("🚀 Iniciando creación del sistema completo de IA...");

    // Crear workflows
    const mainWorkflowId = await createMainAIWorkflow();
    const absenceWorkflowId = await createAbsenceWorkflow();
    const behaviorWorkflowId = await createBehaviorReportWorkflow();
    const approvedReportWorkflowId = await createApprovedReportWorkflow();
    const dailyReportWorkflowId = await createDailyAbsenceReportWorkflow();

    console.log("✅ Sistema de IA creado exitosamente!");
    console.log("📋 Resumen de workflows creados:");
    console.log(`- Workflow Principal: ${mainWorkflowId}`);
    console.log(`- Workflow Ausencias: ${absenceWorkflowId}`);
    console.log(`- Workflow Reportes: ${behaviorWorkflowId}`);
    console.log(`- Workflow Reportes Aprobados: ${approvedReportWorkflowId}`);
    console.log(`- Workflow Reportes Diarios: ${dailyReportWorkflowId}`);

    console.log("\n🔗 URLs de Webhook:");
    console.log(`- Principal: ${N8N_BASE_URL}/webhook/whatsapp-main`);
    console.log(`- Ausencias: ${N8N_BASE_URL}/webhook/process-absence`);
    console.log(`- Reportes: ${N8N_BASE_URL}/webhook/behavior-report`);
    console.log(`- Reportes Aprobados: ${N8N_BASE_URL}/webhook/send-approved-report`);

    console.log("\n⏰ Workflows Programados:");
    console.log(`- Reportes Diarios: 8:00 AM (Lunes a Viernes)`);

    return {
      mainWorkflow: mainWorkflowId,
      absenceWorkflow: absenceWorkflowId,
      behaviorWorkflow: behaviorWorkflowId,
      approvedReportWorkflow: approvedReportWorkflowId,
      dailyReportWorkflow: dailyReportWorkflowId
    };

  } catch (error) {
    console.error("❌ Error creando el sistema de IA:", error);
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createCompleteAISystem()
    .then((result) => {
      console.log("🎉 Sistema de IA configurado exitosamente:", result);
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Error en la configuración:", error);
      process.exit(1);
    });
}

// 3. Workflow de Reportes de Comportamiento
async function createBehaviorReportWorkflow() {
  console.log("Creando workflow de reportes de comportamiento...");

  const workflowData = {
    name: 'Sistema IA - Reportes de Comportamiento',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // Trigger para reportes
      {
        parameters: {
          httpMethod: 'POST',
          path: 'behavior-report',
          responseMode: 'responseNode'
        },
        id: 'behavior-webhook',
        name: 'Trigger Reporte',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },

      // Crear reporte en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              tipo_reporte: 'comportamiento',
              descripcion: '={{ $json.description }}',
              evidencia_urls: '={{ JSON.stringify($json.evidence_urls || []) }}',
              severidad: '={{ $json.severity || "medium" }}',
              reportado_por_telefono: '={{ $json.sender_phone }}',
              reportado_por_nombre: '={{ $json.sender_name }}',
              estudiante_nombre: '={{ $json.student_name }}',
              grado: '={{ $json.grade }}',
              requiere_aprobacion: true,
              estado: 'pendiente_aprobacion',
              fecha_reporte: '={{ new Date().toISOString() }}'
            }
          },
          table: 'reportes_comportamiento'
        },
        id: 'create-report',
        name: 'Crear Reporte',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // Notificar al director para aprobación
      {
        parameters: {
          url: `${BACKEND_URL}/api/dashboard/director-notification`,
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'type',
                value: 'behavior_report_approval'
              },
              {
                name: 'report_id',
                value: '={{ $("Crear Reporte").first().json.id }}'
              },
              {
                name: 'priority',
                value: '={{ $("Trigger Reporte").first().json.severity }}'
              },
              {
                name: 'message',
                value: 'Nuevo reporte de comportamiento requiere aprobación'
              },
              {
                name: 'data',
                value: '={{ $json }}'
              }
            ]
          }
        },
        id: 'notify-director',
        name: 'Notificar Director',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [680, 300]
      },

      // Enviar confirmación de recepción
      {
        parameters: {
          url: 'https://api.waapi.app/instances/YOUR_INSTANCE/client/action/send-message',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              },
              {
                name: 'Authorization',
                value: 'Bearer YOUR_WAAPI_TOKEN'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'chatId',
                value: '={{ $("Trigger Reporte").first().json.sender_phone }}@c.us'
              },
              {
                name: 'message',
                value: '📋 *Reporte Recibido*\n\nHemos recibido su reporte sobre el comportamiento de *{{ $("Trigger Reporte").first().json.student_name }}*.\n\n⏳ El director revisará la información y evidencia proporcionada.\n\n📞 Nos pondremos en contacto con usted una vez que se haya procesado el reporte.\n\n¡Gracias por mantenernos informados!'
              }
            ]
          }
        },
        id: 'send-receipt',
        name: 'Confirmar Recepción',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [900, 300]
      }
    ],

    connections: {
      'Trigger Reporte': {
        main: [
          [
            {
              node: 'Crear Reporte',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Crear Reporte': {
        main: [
          [
            {
              node: 'Notificar Director',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Notificar Director': {
        main: [
          [
            {
              node: 'Confirmar Recepción',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    },

    active: true,
    tags: ['reportes', 'comportamiento', 'aprobacion']
  };

  const result = await makeN8nRequest('POST', '/workflows', workflowData);
  console.log(`Workflow de reportes creado con ID: ${result.id}`);
  return result.id;
}

// 4. Workflow de Envío de Reportes Aprobados
async function createApprovedReportWorkflow() {
  console.log("Creando workflow de envío de reportes aprobados...");

  const workflowData = {
    name: 'Sistema IA - Envío Reportes Aprobados',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // Trigger para reportes aprobados
      {
        parameters: {
          httpMethod: 'POST',
          path: 'send-approved-report',
          responseMode: 'responseNode'
        },
        id: 'approved-webhook',
        name: 'Trigger Reporte Aprobado',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },

      // Obtener detalles del reporte
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              id: '={{ $json.report_id }}'
            }
          },
          table: 'reportes_comportamiento',
          limit: 1
        },
        id: 'get-report',
        name: 'Obtener Reporte',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // Preparar mensaje personalizado
      {
        parameters: {
          jsCode: `
// Preparar mensaje personalizado para el padre
const reportData = $('Obtener Reporte').first().json;
const triggerData = $('Trigger Reporte Aprobado').first().json;

const mensaje = \`📋 *Reporte de Comportamiento - \${reportData.estudiante_nombre}*

Estimado/a \${triggerData.parent_name},

Le informamos sobre el siguiente reporte de comportamiento de su hijo/a *\${reportData.estudiante_nombre}* de \${reportData.grado} grado:

📝 *Descripción:*
\${reportData.descripcion}

\${triggerData.director_comments ? \`💬 *Comentarios del Director:*
\${triggerData.director_comments}

\` : ''}📅 *Fecha del reporte:* \${new Date(reportData.fecha_reporte).toLocaleDateString()}

Agradecemos su atención a este asunto. Si tiene alguna pregunta o desea programar una reunión, no dude en contactarnos.

Atentamente,
Dirección del Colegio Cristiano Jerusalén\`;

return [{
  json: {
    phone_number: triggerData.parent_phone,
    message: mensaje,
    report_id: triggerData.report_id,
    student_name: reportData.estudiante_nombre
  }
}];
`
        },
        id: 'prepare-message',
        name: 'Preparar Mensaje',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [680, 300]
      },

      // Enviar mensaje por WhatsApp
      {
        parameters: {
          url: 'https://api.waapi.app/instances/YOUR_INSTANCE/client/action/send-message',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              },
              {
                name: 'Authorization',
                value: 'Bearer YOUR_WAAPI_TOKEN'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'chatId',
                value: '={{ $json.phone_number }}@c.us'
              },
              {
                name: 'message',
                value: '={{ $json.message }}'
              }
            ]
          }
        },
        id: 'send-whatsapp',
        name: 'Enviar WhatsApp',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [900, 300]
      },

      // Registrar en historial
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              tipo_comunicacion: 'reporte_comportamiento_aprobado',
              destinatario_telefono: '={{ $("Preparar Mensaje").first().json.phone_number }}',
              destinatario_nombre: '={{ $("Trigger Reporte Aprobado").first().json.parent_name }}',
              mensaje_enviado: '={{ $("Preparar Mensaje").first().json.message }}',
              estado_envio: 'enviado',
              fecha_envio: '={{ new Date().toISOString() }}',
              relacionado_con_tabla: 'reportes_comportamiento',
              relacionado_con_id: '={{ $("Preparar Mensaje").first().json.report_id }}',
              institucion_id: 1,
              procesado_por_ia: false
            }
          },
          table: 'historial_comunicaciones'
        },
        id: 'log-communication',
        name: 'Registrar Comunicación',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1120, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      }
    ],

    connections: {
      'Trigger Reporte Aprobado': {
        main: [
          [
            {
              node: 'Obtener Reporte',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Obtener Reporte': {
        main: [
          [
            {
              node: 'Preparar Mensaje',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Mensaje': {
        main: [
          [
            {
              node: 'Enviar WhatsApp',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Enviar WhatsApp': {
        main: [
          [
            {
              node: 'Registrar Comunicación',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    },

    active: true,
    tags: ['reportes', 'aprobados', 'director']
  };

  const result = await makeN8nRequest('POST', '/workflows', workflowData);
  console.log(`Workflow de reportes aprobados creado con ID: ${result.id}`);
  return result.id;
}

// 5. Workflow de Reportes Automáticos de Ausencias Diarias
async function createDailyAbsenceReportWorkflow() {
  console.log("Creando workflow de reportes diarios de ausencias...");

  const workflowData = {
    name: 'Sistema IA - Reportes Diarios Ausencias',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // Trigger programado (cron)
      {
        parameters: {
          rule: {
            interval: [
              {
                field: 'cronExpression',
                expression: '0 8 * * 1-5' // 8:00 AM, Lunes a Viernes
              }
            ]
          }
        },
        id: 'daily-trigger',
        name: 'Trigger Diario 8AM',
        type: 'n8n-nodes-base.cron',
        typeVersion: 1,
        position: [240, 300]
      },

      // Obtener ausencias del día anterior
      {
        parameters: {
          operation: 'executeQuery',
          query: `
            SELECT
              a.id,
              a.fecha_ausencia,
              a.motivo,
              a.justificado,
              al.nombre_completo,
              al.grado_actual,
              al.seccion,
              al.telefono_responsable_principal,
              al.nombre_responsable_principal,
              u.nombre as docente_nombre,
              u.email as docente_email
            FROM ausencias a
            JOIN alumnos al ON a.alumno_id = al.id
            LEFT JOIN asignaciones_docente_grado adg ON al.grado_actual = adg.grado AND al.seccion = adg.seccion
            LEFT JOIN usuarios u ON adg.docente_id = u.id
            WHERE a.fecha_ausencia = CURRENT_DATE - INTERVAL '1 day'
            AND a.notificacion_enviada = false
            ORDER BY al.grado_actual, al.seccion, al.nombre_completo
          `
        },
        id: 'get-absences',
        name: 'Obtener Ausencias Ayer',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // Agrupar ausencias por padre de familia
      {
        parameters: {
          jsCode: `
// Agrupar ausencias por padre de familia
const absences = $input.all().map(item => item.json);

const groupedByParent = absences.reduce((acc, absence) => {
  const phone = absence.telefono_responsable_principal;
  if (!phone) return acc;

  if (!acc[phone]) {
    acc[phone] = {
      parent_name: absence.nombre_responsable_principal,
      phone: phone,
      students: []
    };
  }

  acc[phone].students.push({
    name: absence.nombre_completo,
    grade: absence.grado_actual,
    section: absence.seccion,
    reason: absence.motivo,
    justified: absence.justificado,
    absence_id: absence.id
  });

  return acc;
}, {});

// Convertir a array para procesamiento
const parentNotifications = Object.values(groupedByParent);

return parentNotifications.map(notification => ({ json: notification }));
`
        },
        id: 'group-by-parent',
        name: 'Agrupar por Padre',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [680, 300]
      },

      // Preparar mensaje personalizado para cada padre
      {
        parameters: {
          jsCode: `
// Preparar mensaje personalizado
const parentData = $input.first().json;
const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);
const dateStr = yesterday.toLocaleDateString('es-ES');

let mensaje = \`📋 *Reporte de Ausencias - \${dateStr}*

Estimado/a \${parentData.parent_name},

Le informamos sobre las ausencias registradas el día de ayer:

\`;

parentData.students.forEach((student, index) => {
  mensaje += \`\${index + 1}. *\${student.name}* - \${student.grade} \${student.section}
   📝 Motivo: \${student.reason || 'No especificado'}
   \${student.justified ? '✅ Justificada' : '⚠️ Sin justificar'}

\`;
});

mensaje += \`Si alguna de estas ausencias no es correcta o necesita justificación adicional, por favor contáctenos.

📞 Teléfono: +503 1234-5678
🕐 Horario: 7:00 AM - 3:00 PM

Atentamente,
Colegio Cristiano Jerusalén\`;

return [{
  json: {
    phone: parentData.phone,
    parent_name: parentData.parent_name,
    message: mensaje,
    students: parentData.students,
    absence_ids: parentData.students.map(s => s.absence_id)
  }
}];
`
        },
        id: 'prepare-parent-message',
        name: 'Preparar Mensaje Padre',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 300]
      },

      // Enviar mensaje a padre
      {
        parameters: {
          url: 'https://api.waapi.app/instances/YOUR_INSTANCE/client/action/send-message',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              },
              {
                name: 'Authorization',
                value: 'Bearer YOUR_WAAPI_TOKEN'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'chatId',
                value: '={{ $json.phone }}@c.us'
              },
              {
                name: 'message',
                value: '={{ $json.message }}'
              }
            ]
          }
        },
        id: 'send-to-parent',
        name: 'Enviar a Padre',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [1120, 300]
      },

      // Marcar ausencias como notificadas
      {
        parameters: {
          operation: 'update',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              notificacion_enviada: true,
              fecha_notificacion: '={{ new Date().toISOString() }}'
            }
          },
          table: 'ausencias',
          updateKey: 'id',
          columnToMatchOn: 'id',
          valueToMatchOn: '={{ $("Preparar Mensaje Padre").first().json.absence_ids }}'
        },
        id: 'mark-notified',
        name: 'Marcar Notificadas',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1340, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      }
    ],

    connections: {
      'Trigger Diario 8AM': {
        main: [
          [
            {
              node: 'Obtener Ausencias Ayer',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Obtener Ausencias Ayer': {
        main: [
          [
            {
              node: 'Agrupar por Padre',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Agrupar por Padre': {
        main: [
          [
            {
              node: 'Preparar Mensaje Padre',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Mensaje Padre': {
        main: [
          [
            {
              node: 'Enviar a Padre',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Enviar a Padre': {
        main: [
          [
            {
              node: 'Marcar Notificadas',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    },

    active: true,
    tags: ['ausencias', 'diario', 'automatico']
  };

  const result = await makeN8nRequest('POST', '/workflows', workflowData);
  console.log(`Workflow de reportes diarios creado con ID: ${result.id}`);
  return result.id;
}

module.exports = {
  createCompleteAISystem,
  createMainAIWorkflow,
  createAbsenceWorkflow,
  createBehaviorReportWorkflow,
  createApprovedReportWorkflow,
  createDailyAbsenceReportWorkflow
};
