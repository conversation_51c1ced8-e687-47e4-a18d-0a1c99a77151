const express = require('express');
const router = express.Router();
const db = require('../db');
const authMiddleware = require('../middleware/auth');
const { authorizeRoles } = require('../middleware/auth');

// GET /api/ai-notifications - Obtener notificaciones para el dashboard
router.get('/', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 20, offset = 0, unread_only = false } = req.query;
    
    let query = `
      SELECT 
        n.*,
        u.nombre as creado_por_nombre
      FROM notificaciones_dashboard n
      LEFT JOIN usuarios u ON n.destinatario_id = u.id
      WHERE n.destinatario_id = $1
    `;
    
    const params = [userId];
    
    if (unread_only === 'true') {
      query += ' AND n.leida = false';
    }
    
    query += ' ORDER BY n.fecha_creacion DESC LIMIT $2 OFFSET $3';
    params.push(limit, offset);
    
    const result = await db.query(query, params);
    
    // Contar notificaciones no leídas
    const unreadCount = await db.query(
      'SELECT COUNT(*) as count FROM notificaciones_dashboard WHERE destinatario_id = $1 AND leida = false',
      [userId]
    );
    
    res.json({
      notifications: result.rows,
      unread_count: parseInt(unreadCount.rows[0].count),
      total: result.rows.length
    });
    
  } catch (error) {
    console.error('Error obteniendo notificaciones:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/ai-notifications/director-notification - Crear notificación para director
router.post('/director-notification', async (req, res) => {
  try {
    const { type, report_id, priority, message, data } = req.body;
    
    // Obtener director de la institución
    const directorQuery = await db.query(
      'SELECT id FROM usuarios WHERE rol = $1 AND institucion_id = $2 LIMIT 1',
      ['Director', 1] // Asumiendo institución ID 1
    );
    
    if (directorQuery.rows.length === 0) {
      return res.status(404).json({ error: 'Director no encontrado' });
    }
    
    const directorId = directorQuery.rows[0].id;
    
    // Crear notificación
    const notificationData = {
      tipo_notificacion: type,
      titulo: getTitleByType(type),
      mensaje: message,
      datos_adicionales: JSON.stringify(data),
      destinatario_id: directorId,
      prioridad: priority || 'normal',
      accion_requerida: true,
      url_accion: getActionUrlByType(type, report_id)
    };
    
    const result = await db.query(`
      INSERT INTO notificaciones_dashboard 
      (tipo_notificacion, titulo, mensaje, datos_adicionales, destinatario_id, prioridad, accion_requerida, url_accion, institucion_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 1)
      RETURNING *
    `, [
      notificationData.tipo_notificacion,
      notificationData.titulo,
      notificationData.mensaje,
      notificationData.datos_adicionales,
      notificationData.destinatario_id,
      notificationData.prioridad,
      notificationData.accion_requerida,
      notificationData.url_accion
    ]);
    
    res.status(201).json({
      message: 'Notificación creada exitosamente',
      notification: result.rows[0]
    });
    
  } catch (error) {
    console.error('Error creando notificación:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// PUT /api/ai-notifications/:id/read - Marcar notificación como leída
router.put('/:id/read', authMiddleware, async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user.id;
    
    const result = await db.query(`
      UPDATE notificaciones_dashboard 
      SET leida = true, fecha_lectura = CURRENT_TIMESTAMP
      WHERE id = $1 AND destinatario_id = $2
      RETURNING *
    `, [notificationId, userId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Notificación no encontrada' });
    }
    
    res.json({
      message: 'Notificación marcada como leída',
      notification: result.rows[0]
    });
    
  } catch (error) {
    console.error('Error marcando notificación como leída:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/ai-notifications/approve-report/:id - Aprobar reporte de comportamiento
router.post('/approve-report/:id', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const reportId = req.params.id;
    const { approved, comments, send_to_parent } = req.body;
    const directorId = req.user.id;
    
    // Actualizar estado del reporte
    const newStatus = approved ? 'aprobado' : 'rechazado';
    
    const result = await db.query(`
      UPDATE reportes_comportamiento 
      SET estado = $1, 
          aprobado_por = $2, 
          fecha_aprobacion = CURRENT_TIMESTAMP,
          comentarios_director = $3
      WHERE id = $4
      RETURNING *
    `, [newStatus, directorId, comments, reportId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Reporte no encontrado' });
    }
    
    const report = result.rows[0];
    
    // Si se aprueba y se debe enviar al padre
    if (approved && send_to_parent) {
      // Llamar a n8n para enviar mensaje al padre
      const axios = require('axios');
      const N8N_BASE_URL = process.env.N8N_URL || 'http://localhost:5678';
      
      try {
        await axios.post(`${N8N_BASE_URL}/webhook/send-approved-report`, {
          report_id: reportId,
          parent_phone: report.reportado_por_telefono,
          parent_name: report.reportado_por_nombre,
          student_name: report.estudiante_nombre,
          description: report.descripcion,
          director_comments: comments
        });
        
        // Actualizar estado a enviado
        await db.query(
          'UPDATE reportes_comportamiento SET estado = $1, fecha_envio_padre = CURRENT_TIMESTAMP WHERE id = $2',
          ['enviado', reportId]
        );
        
      } catch (n8nError) {
        console.error('Error enviando mensaje a través de n8n:', n8nError);
      }
    }
    
    res.json({
      message: `Reporte ${approved ? 'aprobado' : 'rechazado'} exitosamente`,
      report: result.rows[0]
    });
    
  } catch (error) {
    console.error('Error procesando aprobación:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/ai-notifications/pending-reports - Obtener reportes pendientes de aprobación
router.get('/pending-reports', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const result = await db.query(`
      SELECT 
        r.*,
        a.nombre_completo as estudiante_nombre_completo,
        a.grado_actual,
        a.seccion
      FROM reportes_comportamiento r
      LEFT JOIN alumnos a ON r.estudiante_id = a.id
      WHERE r.estado = 'pendiente_aprobacion'
      ORDER BY r.fecha_reporte DESC
    `);
    
    res.json({
      reports: result.rows,
      count: result.rows.length
    });
    
  } catch (error) {
    console.error('Error obteniendo reportes pendientes:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Funciones auxiliares
function getTitleByType(type) {
  const titles = {
    'behavior_report_approval': 'Nuevo Reporte de Comportamiento',
    'director_attention_required': 'Atención del Director Requerida',
    'absence_notification': 'Notificación de Ausencia',
    'system_alert': 'Alerta del Sistema'
  };
  
  return titles[type] || 'Notificación';
}

function getActionUrlByType(type, reportId) {
  const urls = {
    'behavior_report_approval': `/dashboard/reports/${reportId}`,
    'director_attention_required': `/dashboard/attention-required`,
    'absence_notification': `/dashboard/absences`,
    'system_alert': '/dashboard/alerts'
  };
  
  return urls[type] || '/dashboard';
}

module.exports = router;
