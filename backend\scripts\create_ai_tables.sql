-- Tablas necesarias para el sistema de IA WhatsApp

-- 1. Actualizar tabla de mensajes WhatsApp
ALTER TABLE mensajes_whatsapp 
ADD COLUMN IF NOT EXISTS nombre_remitente VARCHAR(255),
ADD COLUMN IF NOT EXISTS ai_analysis JSONB,
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2),
ADD COLUMN IF NOT EXISTS requires_approval BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS auto_response_sent BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS response_message TEXT;

-- 2. Tabla para reportes de comportamiento
CREATE TABLE IF NOT EXISTS reportes_comportamiento (
    id SERIAL PRIMARY KEY,
    tipo_reporte VARCHAR(50) NOT NULL DEFAULT 'comportamiento',
    descripcion TEXT NOT NULL,
    evidencia_urls JSONB, -- Array de URLs de fotos/videos
    severidad VARCHAR(20) DEFAULT 'medium' CHECK (severidad IN ('low', 'medium', 'high', 'urgent')),
    reportado_por_telefono VARCHAR(25) NOT NULL,
    reportado_por_nombre VARCHAR(255),
    estudiante_nombre VARCHAR(255),
    estudiante_id INTEGER REFERENCES alumnos(id) ON DELETE SET NULL,
    grado VARCHAR(100),
    seccion VARCHAR(50),
    requiere_aprobacion BOOLEAN DEFAULT TRUE,
    estado VARCHAR(30) DEFAULT 'pendiente' CHECK (estado IN ('pendiente', 'pendiente_aprobacion', 'aprobado', 'rechazado', 'enviado', 'cerrado')),
    aprobado_por INTEGER REFERENCES usuarios(id) ON DELETE SET NULL,
    fecha_aprobacion TIMESTAMP WITH TIME ZONE,
    comentarios_director TEXT,
    mensaje_enviado_padre TEXT,
    fecha_envio_padre TIMESTAMP WITH TIME ZONE,
    fecha_reporte TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    institucion_id INTEGER REFERENCES instituciones(id) ON DELETE CASCADE,
    CONSTRAINT fk_reportado_por FOREIGN KEY (aprobado_por) REFERENCES usuarios(id)
);

-- 3. Tabla para notificaciones del dashboard
CREATE TABLE IF NOT EXISTS notificaciones_dashboard (
    id SERIAL PRIMARY KEY,
    tipo_notificacion VARCHAR(50) NOT NULL,
    titulo VARCHAR(255) NOT NULL,
    mensaje TEXT NOT NULL,
    datos_adicionales JSONB,
    destinatario_id INTEGER NOT NULL REFERENCES usuarios(id) ON DELETE CASCADE,
    prioridad VARCHAR(20) DEFAULT 'normal' CHECK (prioridad IN ('low', 'normal', 'high', 'urgent')),
    leida BOOLEAN DEFAULT FALSE,
    accion_requerida BOOLEAN DEFAULT FALSE,
    url_accion VARCHAR(512),
    fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    fecha_lectura TIMESTAMP WITH TIME ZONE,
    institucion_id INTEGER REFERENCES instituciones(id) ON DELETE CASCADE
);

-- 4. Tabla para configuración de horarios automáticos
CREATE TABLE IF NOT EXISTS configuracion_horarios (
    id SERIAL PRIMARY KEY,
    institucion_id INTEGER NOT NULL REFERENCES instituciones(id) ON DELETE CASCADE,
    tipo_notificacion VARCHAR(50) NOT NULL, -- 'ausencias_diarias', 'reportes_semanales', etc.
    hora_envio TIME NOT NULL,
    dias_semana INTEGER[] DEFAULT '{1,2,3,4,5}', -- 1=Lunes, 7=Domingo
    activo BOOLEAN DEFAULT TRUE,
    ultimo_envio TIMESTAMP WITH TIME ZONE,
    configuracion_adicional JSONB,
    creado_por INTEGER REFERENCES usuarios(id) ON DELETE SET NULL,
    fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. Tabla para historial de comunicaciones automatizadas
CREATE TABLE IF NOT EXISTS historial_comunicaciones (
    id SERIAL PRIMARY KEY,
    tipo_comunicacion VARCHAR(50) NOT NULL,
    destinatario_telefono VARCHAR(25) NOT NULL,
    destinatario_nombre VARCHAR(255),
    mensaje_enviado TEXT NOT NULL,
    estado_envio VARCHAR(30) DEFAULT 'enviado' CHECK (estado_envio IN ('enviado', 'fallido', 'pendiente')),
    respuesta_recibida TEXT,
    fecha_envio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    fecha_respuesta TIMESTAMP WITH TIME ZONE,
    relacionado_con_tabla VARCHAR(50), -- 'ausencias', 'reportes_comportamiento', etc.
    relacionado_con_id INTEGER,
    institucion_id INTEGER REFERENCES instituciones(id) ON DELETE CASCADE,
    procesado_por_ia BOOLEAN DEFAULT TRUE
);

-- 6. Actualizar tabla de ausencias con campos adicionales
ALTER TABLE ausencias 
ADD COLUMN IF NOT EXISTS reportado_por_telefono VARCHAR(25),
ADD COLUMN IF NOT EXISTS reportado_por_nombre VARCHAR(255),
ADD COLUMN IF NOT EXISTS grado_reportado VARCHAR(100),
ADD COLUMN IF NOT EXISTS procesado_por_ia BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS notificacion_enviada BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS fecha_notificacion TIMESTAMP WITH TIME ZONE;

-- 7. Tabla para asignaciones docente-grado-estudiante
CREATE TABLE IF NOT EXISTS asignaciones_docente_grado (
    id SERIAL PRIMARY KEY,
    docente_id INTEGER NOT NULL REFERENCES usuarios(id) ON DELETE CASCADE,
    grado VARCHAR(100) NOT NULL,
    seccion VARCHAR(50),
    materia VARCHAR(100),
    es_tutor BOOLEAN DEFAULT FALSE,
    activo BOOLEAN DEFAULT TRUE,
    institucion_id INTEGER NOT NULL REFERENCES instituciones(id) ON DELETE CASCADE,
    asignado_por INTEGER REFERENCES usuarios(id) ON DELETE SET NULL,
    fecha_asignacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(docente_id, grado, seccion, materia, institucion_id)
);

-- 8. Tabla para configuración de IA por institución
CREATE TABLE IF NOT EXISTS configuracion_ia (
    id SERIAL PRIMARY KEY,
    institucion_id INTEGER NOT NULL REFERENCES instituciones(id) ON DELETE CASCADE,
    chatgpt_api_key VARCHAR(255),
    modelo_ia VARCHAR(50) DEFAULT 'gpt-4',
    temperatura DECIMAL(2,1) DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 800,
    auto_respuesta_ausencias BOOLEAN DEFAULT TRUE,
    auto_respuesta_consultas BOOLEAN DEFAULT TRUE,
    requiere_aprobacion_reportes BOOLEAN DEFAULT TRUE,
    horario_atencion_inicio TIME DEFAULT '07:00',
    horario_atencion_fin TIME DEFAULT '15:00',
    mensaje_fuera_horario TEXT DEFAULT 'Gracias por contactarnos. Nuestro horario de atención es de 7:00 AM a 3:00 PM. Su mensaje será atendido en el próximo horario hábil.',
    configuracion_adicional JSONB,
    activo BOOLEAN DEFAULT TRUE,
    fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Índices para mejorar rendimiento
CREATE INDEX IF NOT EXISTS idx_mensajes_whatsapp_telefono ON mensajes_whatsapp(telefono_remitente);
CREATE INDEX IF NOT EXISTS idx_mensajes_whatsapp_fecha ON mensajes_whatsapp(fecha_recepcion);
CREATE INDEX IF NOT EXISTS idx_mensajes_whatsapp_tipo ON mensajes_whatsapp(tipo_mensaje);
CREATE INDEX IF NOT EXISTS idx_reportes_comportamiento_estado ON reportes_comportamiento(estado);
CREATE INDEX IF NOT EXISTS idx_reportes_comportamiento_fecha ON reportes_comportamiento(fecha_reporte);
CREATE INDEX IF NOT EXISTS idx_notificaciones_dashboard_destinatario ON notificaciones_dashboard(destinatario_id, leida);
CREATE INDEX IF NOT EXISTS idx_ausencias_fecha ON ausencias(fecha_ausencia);
CREATE INDEX IF NOT EXISTS idx_ausencias_alumno ON ausencias(alumno_id);

-- Insertar configuración por defecto
INSERT INTO configuracion_ia (institucion_id, activo) 
SELECT id, true FROM instituciones 
WHERE NOT EXISTS (SELECT 1 FROM configuracion_ia WHERE institucion_id = instituciones.id);

-- Comentarios para documentación
COMMENT ON TABLE reportes_comportamiento IS 'Almacena reportes de comportamiento estudiantil con evidencia multimedia';
COMMENT ON TABLE notificaciones_dashboard IS 'Sistema de notificaciones en tiempo real para el dashboard';
COMMENT ON TABLE configuracion_horarios IS 'Configuración de envíos automáticos programados';
COMMENT ON TABLE historial_comunicaciones IS 'Registro completo de todas las comunicaciones automatizadas';
COMMENT ON TABLE asignaciones_docente_grado IS 'Asignaciones de docentes a grados y materias';
COMMENT ON TABLE configuracion_ia IS 'Configuración específica de IA por institución';
