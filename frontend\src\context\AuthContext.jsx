import React, { createContext, useState, useEffect, useContext } from 'react';

// API Base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

// Crear el contexto
const AuthContext = createContext(null);

// Hook para usar el contexto
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

// Proveedor de autenticación
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Función para limpiar el almacenamiento
  const clearStorage = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('userData');
    console.log('🧹 Storage limpiado');
  };

  // Función para hacer login
  const login = async (email, password) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🔐 Intentando login con:', { email, password: '***' });

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();
      console.log('📡 Respuesta del servidor:', data);

      if (response.ok && data.token && data.user) {
        // Guardar en localStorage
        localStorage.setItem('token', data.token);
        localStorage.setItem('authToken', data.token); // Compatibilidad
        localStorage.setItem('userData', JSON.stringify(data.user));

        // Actualizar estado
        setToken(data.token);
        setUser(data.user);

        console.log('✅ Login exitoso:', data.user);
        return { success: true, user: data.user };
      } else {
        const errorMsg = data.error || data.message || 'Error de autenticación';
        setError(errorMsg);
        console.log('❌ Error de login:', errorMsg);
        return { success: false, error: errorMsg };
      }
    } catch (error) {
      const errorMsg = 'Error de conexión con el servidor';
      setError(errorMsg);
      console.error('❌ Error de conexión:', error);
      return { success: false, error: errorMsg };
    } finally {
      setIsLoading(false);
    }
  };

  // Función de logout
  const logout = () => {
    clearStorage();
    setToken(null);
    setUser(null);
    setError(null);
    console.log('✅ Logout exitoso');
  };

  // Función para actualizar usuario
  const updateUser = (userData) => {
    if (userData) {
      localStorage.setItem('userData', JSON.stringify(userData));
      setUser(userData);
      console.log('✅ Usuario actualizado:', userData);
    }
  };

  // Función para verificar si es superadministrador
  const isSuperAdmin = () => {
    if (!user) {
      console.log('🔍 isSuperAdmin: No hay usuario');
      return false;
    }

    console.log('🔍 isSuperAdmin verificando:', {
      email: user.email,
      rol: user.rol,
      nombre: user.nombre
    });

    // Verificar por email (método más confiable)
    if (user.email === '<EMAIL>') {
      console.log('✅ isSuperAdmin: Verificado por email');
      return true;
    }

    // Verificar por rol
    const superAdminRoles = [
      'Superadministrador',
      'superadministrador',
      'SuperAdministrador',
      'SUPERADMINISTRADOR'
    ];

    const isSuperByRole = superAdminRoles.includes(user.rol);
    console.log('🔍 isSuperAdmin por rol:', { rol: user.rol, isSuperByRole });

    return isSuperByRole;
  };

  // Función para verificar permisos
  const hasPermission = (permission) => {
    if (!user) {
      console.log('🔍 hasPermission: No hay usuario');
      return false;
    }

    if (isSuperAdmin()) {
      console.log('✅ hasPermission: Superadmin tiene todos los permisos');
      return true;
    }

    const rolePermissions = {
      'Director': ['admin', 'director'],
      'Coordinador Académico': ['coordinador'],
      'Secretario': ['secretario'],
      'Docente': ['docente']
    };

    const userPermissions = rolePermissions[user.rol] || [];
    const hasAccess = userPermissions.includes(permission);

    console.log('🔍 hasPermission:', {
      permission,
      userRole: user.rol,
      userPermissions,
      hasAccess
    });

    return hasAccess;
  };

  // Inicialización del contexto
  useEffect(() => {
    const initializeAuth = () => {
      try {
        console.log('🔄 Inicializando autenticación...');

        // Buscar token en localStorage
        const storedToken = localStorage.getItem('token') || localStorage.getItem('authToken');
        const storedUserData = localStorage.getItem('userData');

        if (storedToken && storedUserData) {
          try {
            const userData = JSON.parse(storedUserData);
            setToken(storedToken);
            setUser(userData);
            console.log('✅ Sesión restaurada:', userData);
          } catch (error) {
            console.error('❌ Error parsing stored data:', error);
            clearStorage();
          }
        } else {
          console.log('ℹ️ No hay sesión previa');
        }
      } catch (error) {
        console.error('❌ Error initializing auth:', error);
        clearStorage();
      } finally {
        setIsLoading(false);
        console.log('✅ Autenticación inicializada');
      }
    };

    initializeAuth();
  }, []);

  // Valor del contexto
  const contextValue = {
    user,
    currentUser: user, // Alias para compatibilidad
    token,
    isAuthenticated: !!(token && user),
    isLoading,
    error,
    login,
    logout,
    updateUser,
    isSuperAdmin,
    hasPermission
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Exportar por defecto el hook
export default useAuth;
