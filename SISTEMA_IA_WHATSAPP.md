# Sistema de IA WhatsApp - Colegio Cristiano Jerusalén

## 📋 Descripción General

Este sistema implementa una solución completa de inteligencia artificial para gestionar comunicaciones de WhatsApp entre padres de familia, docentes y la dirección del colegio. Utiliza ChatGPT para análisis automático de mensajes y n8n para orquestación de workflows.

## 🎯 Funcionalidades Principales

### 1. **Análisis Inteligente de Mensajes**
- **IA con Memoria**: ChatGPT analiza mensajes con contexto de conversaciones anteriores
- **Clasificación Automática**: Identifica tipos de mensaje (ausencias, consultas, reportes, etc.)
- **Respuestas Contextuales**: Genera respuestas personalizadas basadas en el historial

### 2. **Gestión de Ausencias**
- **Detección Automática**: La IA identifica reportes de ausencia en mensajes
- **Procesamiento Inmediato**: Registra ausencias automáticamente en la base de datos
- **Notificación a Docentes**: Informa automáticamente al docente correspondiente
- **Confirmación a Padres**: Envía confirmación automática al padre de familia

### 3. **Reportes de Comportamiento**
- **Recepción con Evidencia**: Acepta reportes con fotos y videos
- **Sistema de Aprobación**: Requiere aprobación del director para envío
- **Escalamiento Inteligente**: La IA determina la severidad del reporte
- **Notificaciones al Director**: Alertas en tiempo real en el dashboard

### 4. **Sistema de Aprobaciones del Director**
- **Dashboard de Aprobaciones**: Interface para revisar reportes pendientes
- **Aprobación/Rechazo**: Sistema de aprobación con comentarios
- **Envío Automático**: Una vez aprobado, se envía automáticamente al padre
- **Historial Completo**: Registro de todas las decisiones y comunicaciones

### 5. **Reportes Automáticos Diarios**
- **Envío Programado**: Reportes de ausencias enviados automáticamente a las 8:00 AM
- **Agrupación Inteligente**: Agrupa múltiples ausencias por familia
- **Personalización**: Mensajes personalizados para cada padre de familia

## 🏗️ Arquitectura del Sistema

### Componentes Principales

1. **n8n (Orquestador de Workflows)**
   - 5 workflows especializados
   - Integración con ChatGPT
   - Conexión con base de datos PostgreSQL
   - Webhooks para comunicación externa

2. **ChatGPT (Motor de IA)**
   - Análisis de mensajes con contexto
   - Clasificación automática
   - Generación de respuestas
   - Extracción de datos estructurados

3. **Base de Datos PostgreSQL**
   - Almacenamiento de mensajes y análisis
   - Historial de comunicaciones
   - Reportes de comportamiento
   - Configuración del sistema

4. **Backend API (Node.js)**
   - Rutas para notificaciones
   - Sistema de aprobaciones
   - Dashboard de director
   - Integración con n8n

## 📊 Workflows Implementados

### 1. **Workflow Principal - Análisis de Mensajes**
- **Trigger**: Webhook de WhatsApp
- **Proceso**: 
  1. Guardar mensaje en BD
  2. Obtener historial del usuario
  3. Obtener estudiantes asociados
  4. Preparar contexto para ChatGPT
  5. Análisis con IA
  6. Actualizar mensaje con análisis

### 2. **Workflow de Ausencias**
- **Trigger**: Webhook interno
- **Proceso**:
  1. Buscar estudiante en BD
  2. Crear registro de ausencia
  3. Notificar al docente
  4. Enviar confirmación al padre

### 3. **Workflow de Reportes de Comportamiento**
- **Trigger**: Webhook interno
- **Proceso**:
  1. Crear reporte en BD
  2. Notificar al director
  3. Enviar confirmación de recepción

### 4. **Workflow de Reportes Aprobados**
- **Trigger**: Webhook interno
- **Proceso**:
  1. Obtener detalles del reporte
  2. Preparar mensaje personalizado
  3. Enviar por WhatsApp
  4. Registrar en historial

### 5. **Workflow de Reportes Diarios**
- **Trigger**: Cron (8:00 AM, Lunes-Viernes)
- **Proceso**:
  1. Obtener ausencias del día anterior
  2. Agrupar por padre de familia
  3. Preparar mensajes personalizados
  4. Enviar a cada padre
  5. Marcar como notificadas

## 🗄️ Estructura de Base de Datos

### Tablas Principales

1. **mensajes_whatsapp**
   - Almacena todos los mensajes recibidos
   - Incluye análisis de IA y clasificación
   - Campos: telefono_remitente, texto_mensaje, ai_analysis, tipo_mensaje

2. **reportes_comportamiento**
   - Reportes con evidencia multimedia
   - Sistema de estados y aprobaciones
   - Campos: descripcion, evidencia_urls, severidad, estado, aprobado_por

3. **notificaciones_dashboard**
   - Notificaciones en tiempo real para directores
   - Sistema de prioridades y acciones
   - Campos: tipo_notificacion, mensaje, prioridad, leida, accion_requerida

4. **historial_comunicaciones**
   - Registro completo de todas las comunicaciones
   - Trazabilidad de mensajes enviados
   - Campos: tipo_comunicacion, mensaje_enviado, estado_envio

5. **configuracion_ia**
   - Configuración específica por institución
   - Parámetros de IA y horarios
   - Campos: chatgpt_api_key, auto_respuesta_ausencias, horario_atencion

## 🔧 Configuración e Instalación

### Prerrequisitos
- Docker y Docker Compose
- API Key de OpenAI (ChatGPT)
- Cuenta de WaAPI para WhatsApp
- PostgreSQL

### Pasos de Instalación

1. **Ejecutar Script de Configuración**
   ```bash
   cd backend/scripts
   node setup_complete_ai_system.js
   ```

2. **Configurar Variables de Entorno**
   ```env
   CHATGPT_API_KEY=sk-your-openai-api-key
   N8N_API_KEY=your-n8n-api-key
   WAAPI_TOKEN=your-waapi-token
   ```

3. **Configurar Credenciales en n8n**
   - PostgreSQL: Conexión a base de datos
   - OpenAI: API key de ChatGPT
   - WaAPI: Token de WhatsApp

4. **Configurar Webhooks en WaAPI**
   - URL Principal: `https://n8n.echolab.xyz/webhook/whatsapp-main`

## 📱 Flujo de Uso

### Para Padres de Familia

1. **Reportar Ausencia**
   ```
   "Buenos días, mi hijo Juan Pérez de 5to grado no asistirá hoy por enfermedad"
   ```
   - ✅ IA detecta ausencia automáticamente
   - ✅ Registra en sistema
   - ✅ Notifica al docente
   - ✅ Confirma al padre

2. **Hacer Consulta**
   ```
   "¿A qué hora salen los estudiantes de 3er grado?"
   ```
   - ✅ IA responde automáticamente
   - ✅ Usa información del colegio

3. **Reportar Comportamiento**
   ```
   "Quiero reportar un problema con mi hijo en el recreo" + foto/video
   ```
   - ✅ IA detecta reporte con evidencia
   - ✅ Notifica al director para aprobación
   - ✅ Confirma recepción al padre

### Para Director

1. **Recibir Notificaciones**
   - Dashboard muestra reportes pendientes
   - Notificaciones en tiempo real
   - Sistema de prioridades

2. **Aprobar/Rechazar Reportes**
   - Revisar evidencia y descripción
   - Agregar comentarios
   - Aprobar o rechazar envío

3. **Monitorear Sistema**
   - Ver estadísticas de mensajes
   - Revisar historial de comunicaciones
   - Configurar parámetros de IA

### Para Docentes

1. **Recibir Notificaciones**
   - Ausencias de estudiantes
   - Reportes aprobados por director
   - Comunicaciones importantes

## 🔐 Seguridad y Privacidad

- **Encriptación**: Todas las comunicaciones están encriptadas
- **Autenticación**: Sistema de roles y permisos
- **Auditoría**: Registro completo de todas las acciones
- **Privacidad**: Datos personales protegidos según normativas

## 📈 Métricas y Monitoreo

- **Mensajes Procesados**: Cantidad diaria/semanal/mensual
- **Precisión de IA**: Porcentaje de clasificación correcta
- **Tiempo de Respuesta**: Velocidad de procesamiento
- **Satisfacción**: Feedback de usuarios

## 🚀 Próximas Mejoras

1. **Análisis de Sentimientos**: Detectar emociones en mensajes
2. **Respuestas por Voz**: Integración con síntesis de voz
3. **Chatbot Multiidioma**: Soporte para múltiples idiomas
4. **Integración con Calendario**: Eventos y actividades escolares
5. **Reportes Avanzados**: Analytics y dashboards mejorados

## 📞 Soporte Técnico

Para soporte técnico o consultas sobre el sistema:
- **Email**: <EMAIL>
- **Teléfono**: +503 1234-5678
- **Horario**: Lunes a Viernes, 7:00 AM - 3:00 PM
