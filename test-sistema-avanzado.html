<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sistema Avanzado WhatsApp - CCJAP</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        .result {
            margin-top: 15px;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            line-height: 1.4;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Test Sistema Avanzado WhatsApp - CCJAP</h1>
        <p>Herramienta completa para probar las mejoras implementadas en el sistema de automatización</p>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="tests-total">0</div>
                <div class="stat-label">Tests Ejecutados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="tests-success">0</div>
                <div class="stat-label">Exitosos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="tests-failed">0</div>
                <div class="stat-label">Fallidos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="response-time">0ms</div>
                <div class="stat-label">Tiempo Promedio</div>
            </div>
        </div>

        <div class="test-section">
            <h3><span class="emoji">🧠</span>1. Patrones Avanzados de Reconocimiento</h3>
            <button onclick="testAusenciaCompleta()">📝 Ausencia Completa</button>
            <button onclick="testEmergencia()">🚨 Emergencia</button>
            <button onclick="testConsultaCalificaciones()">📊 Consulta Calificaciones</button>
            <button onclick="testConsultaTareas()">📚 Consulta Tareas</button>
            <button onclick="testConsultaHorarios()">🕐 Consulta Horarios</button>
            <button onclick="testConsultaEventos()">🎉 Consulta Eventos</button>
            <button onclick="testConsultaAdministrativa()">🏢 Consulta Administrativa</button>
            <button onclick="testSaludoInteligente()">👋 Saludo Inteligente</button>
            <div id="patrones-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3><span class="emoji">🤖</span>2. Respuestas Sofisticadas</h3>
            <button onclick="testRespuestaContextual()">💬 Respuesta Contextual</button>
            <button onclick="testRespuestaUrgente()">⚡ Respuesta Urgente</button>
            <button onclick="testRespuestaPersonalizada()">👤 Respuesta Personalizada</button>
            <button onclick="testRespuestaMultiple()">🔄 Respuestas Múltiples</button>
            <div id="respuestas-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3><span class="emoji">📊</span>3. Reportes Automáticos</h3>
            <button onclick="testCrearReporte()">📝 Crear Reporte</button>
            <button onclick="testReporteDiario()">📅 Reporte Diario</button>
            <button onclick="testReporteSemanal()">📈 Reporte Semanal</button>
            <button onclick="testEstadisticas()">📊 Estadísticas</button>
            <div id="reportes-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3><span class="emoji">🔔</span>4. Sistema de Notificaciones</h3>
            <button onclick="testNotificacionDirector()">👨‍💼 Notificar Director</button>
            <button onclick="testNotificacionDocente()">👩‍🏫 Notificar Docente</button>
            <button onclick="testNotificacionAdmin()">🏢 Notificar Administración</button>
            <button onclick="testNotificacionesPendientes()">📋 Notificaciones Pendientes</button>
            <div id="notificaciones-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3><span class="emoji">🔄</span>5. Flujo Completo de Procesamiento</h3>
            <button onclick="testFlujoCompleto()">🚀 Flujo Completo</button>
            <button onclick="testMultiplesMensajes()">📱 Múltiples Mensajes</button>
            <button onclick="testRendimiento()">⚡ Test de Rendimiento</button>
            <button onclick="limpiarTodo()">🧹 Limpiar Todo</button>
            <div id="flujo-result" class="result"></div>
        </div>
    </div>

    <script>
        let testsTotal = 0;
        let testsSuccess = 0;
        let testsFailed = 0;
        let responseTimes = [];

        function updateStats() {
            document.getElementById('tests-total').textContent = testsTotal;
            document.getElementById('tests-success').textContent = testsSuccess;
            document.getElementById('tests-failed').textContent = testsFailed;

            const avgTime = responseTimes.length > 0
                ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
                : 0;
            document.getElementById('response-time').textContent = avgTime + 'ms';
        }

        function log(message, resultId, type = 'info') {
            const resultDiv = document.getElementById(resultId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;

            if (resultDiv.innerHTML === '') {
                resultDiv.innerHTML = logEntry;
            } else {
                resultDiv.innerHTML += logEntry;
            }

            resultDiv.className = `result ${type}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        async function makeRequest(url, method = 'GET', body = null, headers = {}) {
            const startTime = Date.now();
            testsTotal++;

            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        ...headers
                    }
                };

                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(url, options);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                responseTimes.push(responseTime);

                if (response.ok) {
                    testsSuccess++;
                    const data = await response.json();
                    updateStats();
                    return { success: true, data, responseTime };
                } else {
                    testsFailed++;
                    updateStats();
                    return { success: false, error: `HTTP ${response.status}`, responseTime };
                }
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                responseTimes.push(responseTime);
                testsFailed++;
                updateStats();
                return { success: false, error: error.message, responseTime };
            }
        }

        async function getAuthToken() {
            const result = await makeRequest('http://localhost:3001/api/auth/login', 'POST', {
                email: '<EMAIL>',
                password: 'admin123'
            });

            if (result.success) {
                return result.data.token;
            }
            throw new Error('No se pudo obtener token de autenticación');
        }

        // 1. PATRONES AVANZADOS DE RECONOCIMIENTO
        async function testAusenciaCompleta() {
            log('🧠 Probando detección de ausencia completa...', 'patrones-result');

            const mensaje = {
                from: "+50312345678",
                body: "Ausente: María José Rodríguez, 3° grado sección A por cita médica",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Ausencia detectada correctamente (${result.responseTime}ms)`, 'patrones-result', 'success');
                log(`📋 Respuesta: ${JSON.stringify(result.data, null, 2)}`, 'patrones-result', 'info');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        async function testEmergencia() {
            log('🚨 Probando detección de emergencia...', 'patrones-result');

            const mensaje = {
                from: "+50387654321",
                body: "EMERGENCIA: Mi hijo tuvo un accidente, necesito hablar con el director urgentemente",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Emergencia detectada correctamente (${result.responseTime}ms)`, 'patrones-result', 'success');
                log(`🚨 Prioridad alta activada`, 'patrones-result', 'warning');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        async function testConsultaCalificaciones() {
            log('📊 Probando consulta de calificaciones...', 'patrones-result');

            const mensaje = {
                from: "+50398765432",
                body: "Hola, quisiera consultar las calificaciones de matemáticas de mi hijo Juan del 2° grado",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Consulta de calificaciones detectada (${result.responseTime}ms)`, 'patrones-result', 'success');
                log(`📊 Materia específica: Matemáticas`, 'patrones-result', 'info');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        async function testConsultaTareas() {
            log('📚 Probando consulta de tareas...', 'patrones-result');

            const mensaje = {
                from: "+50376543210",
                body: "¿Cuáles son las tareas de hoy para el 1° grado?",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Consulta de tareas detectada (${result.responseTime}ms)`, 'patrones-result', 'success');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        async function testConsultaHorarios() {
            log('🕐 Probando consulta de horarios...', 'patrones-result');

            const mensaje = {
                from: "+50355667788",
                body: "¿A qué hora es la entrada y salida del colegio?",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Consulta de horarios detectada (${result.responseTime}ms)`, 'patrones-result', 'success');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        async function testConsultaEventos() {
            log('🎉 Probando consulta de eventos...', 'patrones-result');

            const mensaje = {
                from: "+50344556677",
                body: "¿Cuándo es la próxima reunión de padres de familia?",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Consulta de eventos detectada (${result.responseTime}ms)`, 'patrones-result', 'success');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        async function testConsultaAdministrativa() {
            log('🏢 Probando consulta administrativa...', 'patrones-result');

            const mensaje = {
                from: "+50333445566",
                body: "Necesito información sobre el pago de la mensualidad y documentos para matrícula",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Consulta administrativa detectada (${result.responseTime}ms)`, 'patrones-result', 'success');
                log(`🏢 Prioridad alta para administración`, 'patrones-result', 'warning');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        async function testSaludoInteligente() {
            log('👋 Probando saludo inteligente...', 'patrones-result');

            const hora = new Date().getHours();
            let saludo = "Hola";
            if (hora < 12) saludo = "Buenos días";
            else if (hora < 18) saludo = "Buenas tardes";
            else saludo = "Buenas noches";

            const mensaje = {
                from: "+50322334455",
                body: saludo + ", ¿cómo están?",
                timestamp: new Date().toISOString()
            };

            const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

            if (result.success) {
                log(`✅ Saludo inteligente detectado (${result.responseTime}ms)`, 'patrones-result', 'success');
                log(`👋 Saludo contextual: ${saludo}`, 'patrones-result', 'info');
            } else {
                log(`❌ Error: ${result.error}`, 'patrones-result', 'error');
            }
        }

        // 3. REPORTES AUTOMÁTICOS
        async function testCrearReporte() {
            log('📝 Probando creación de reporte automático...', 'reportes-result');

            const reporteData = {
                tipo: 'reporte_ausencia',
                estudiante: 'Ana María López',
                grado: '2',
                seccion: 'B',
                motivo: 'Cita médica',
                fecha: new Date().toISOString().split('T')[0],
                hora: new Date().toLocaleTimeString(),
                telefono_padre: '+50312345678',
                prioridad: 'normal',
                requiere_seguimiento: false,
                acciones_sugeridas: ['notificar_docente', 'actualizar_asistencia']
            };

            const result = await makeRequest('http://localhost:3001/api/reportes/ausencias', 'POST', reporteData);

            if (result.success) {
                log(`✅ Reporte creado exitosamente (${result.responseTime}ms)`, 'reportes-result', 'success');
                log(`📊 ID del reporte: ${result.data.reporte.id}`, 'reportes-result', 'info');
            } else {
                log(`❌ Error: ${result.error}`, 'reportes-result', 'error');
            }
        }

        async function testReporteDiario() {
            log('📅 Probando reporte diario de ausencias...', 'reportes-result');

            try {
                const token = await getAuthToken();
                const fecha = new Date().toISOString().split('T')[0];

                const result = await makeRequest(
                    `http://localhost:3001/api/reportes/ausencias/diario?fecha=${fecha}`,
                    'GET',
                    null,
                    { 'Authorization': `Bearer ${token}` }
                );

                if (result.success) {
                    log(`✅ Reporte diario generado (${result.responseTime}ms)`, 'reportes-result', 'success');
                    log(`📊 Total ausencias: ${result.data.resumen.total_ausencias}`, 'reportes-result', 'info');
                    log(`🎓 Grados afectados: ${result.data.resumen.grados_afectados}`, 'reportes-result', 'info');
                } else {
                    log(`❌ Error: ${result.error}`, 'reportes-result', 'error');
                }
            } catch (error) {
                log(`❌ Error de autenticación: ${error.message}`, 'reportes-result', 'error');
            }
        }

        async function testReporteSemanal() {
            log('📈 Probando reporte semanal de ausencias...', 'reportes-result');

            try {
                const token = await getAuthToken();
                const fechaFin = new Date().toISOString().split('T')[0];

                const result = await makeRequest(
                    `http://localhost:3001/api/reportes/ausencias/semanal?fecha_fin=${fechaFin}`,
                    'GET',
                    null,
                    { 'Authorization': `Bearer ${token}` }
                );

                if (result.success) {
                    log(`✅ Reporte semanal generado (${result.responseTime}ms)`, 'reportes-result', 'success');
                    log(`📅 Período: ${result.data.periodo.fecha_inicio} - ${result.data.periodo.fecha_fin}`, 'reportes-result', 'info');
                    log(`📊 Días con datos: ${result.data.datos_diarios.length}`, 'reportes-result', 'info');
                } else {
                    log(`❌ Error: ${result.error}`, 'reportes-result', 'error');
                }
            } catch (error) {
                log(`❌ Error de autenticación: ${error.message}`, 'reportes-result', 'error');
            }
        }

        async function testEstadisticas() {
            log('📊 Probando estadísticas de notificaciones...', 'reportes-result');

            try {
                const token = await getAuthToken();

                const result = await makeRequest(
                    'http://localhost:3001/api/notificaciones/estadisticas',
                    'GET',
                    null,
                    { 'Authorization': `Bearer ${token}` }
                );

                if (result.success) {
                    log(`✅ Estadísticas obtenidas (${result.responseTime}ms)`, 'reportes-result', 'success');
                    log(`📈 Tipos de notificaciones: ${result.data.estadisticas.length}`, 'reportes-result', 'info');
                } else {
                    log(`❌ Error: ${result.error}`, 'reportes-result', 'error');
                }
            } catch (error) {
                log(`❌ Error de autenticación: ${error.message}`, 'reportes-result', 'error');
            }
        }

        // 4. SISTEMA DE NOTIFICACIONES
        async function testNotificacionDirector() {
            log('👨‍💼 Probando notificación al director...', 'notificaciones-result');

            const notificacionData = {
                tipo: 'director',
                mensaje: '🚨 URGENTE: Emergencia reportada por padre de familia',
                telefono: '+50312345678',
                prioridad: 'alta',
                requiere_atencion: true
            };

            const result = await makeRequest('http://localhost:3001/api/notificaciones/enviar', 'POST', notificacionData);

            if (result.success) {
                log(`✅ Notificación al director enviada (${result.responseTime}ms)`, 'notificaciones-result', 'success');
                log(`👥 Destinatarios: ${result.data.destinatarios}`, 'notificaciones-result', 'info');
            } else {
                log(`❌ Error: ${result.error}`, 'notificaciones-result', 'error');
            }
        }

        async function testNotificacionDocente() {
            log('👩‍🏫 Probando notificación al docente...', 'notificaciones-result');

            const notificacionData = {
                tipo: 'docente',
                mensaje: '📝 Ausencia reportada: Carlos Martínez - 3°A',
                grado: '3',
                seccion: 'A',
                motivo: 'Cita médica',
                prioridad: 'normal'
            };

            const result = await makeRequest('http://localhost:3001/api/notificaciones/enviar', 'POST', notificacionData);

            if (result.success) {
                log(`✅ Notificación al docente enviada (${result.responseTime}ms)`, 'notificaciones-result', 'success');
                log(`👥 Destinatarios: ${result.data.destinatarios}`, 'notificaciones-result', 'info');
            } else {
                log(`❌ Error: ${result.error}`, 'notificaciones-result', 'error');
            }
        }

        async function testNotificacionAdmin() {
            log('🏢 Probando notificación a administración...', 'notificaciones-result');

            const notificacionData = {
                tipo: 'administracion',
                mensaje: '🏢 Consulta administrativa: Información sobre pagos y documentos',
                telefono: '+50387654321',
                requiere_atencion: true,
                prioridad: 'alta'
            };

            const result = await makeRequest('http://localhost:3001/api/notificaciones/enviar', 'POST', notificacionData);

            if (result.success) {
                log(`✅ Notificación a administración enviada (${result.responseTime}ms)`, 'notificaciones-result', 'success');
                log(`👥 Destinatarios: ${result.data.destinatarios}`, 'notificaciones-result', 'info');
            } else {
                log(`❌ Error: ${result.error}`, 'notificaciones-result', 'error');
            }
        }

        async function testNotificacionesPendientes() {
            log('📋 Probando obtener notificaciones pendientes...', 'notificaciones-result');

            try {
                const token = await getAuthToken();

                const result = await makeRequest(
                    'http://localhost:3001/api/notificaciones/pendientes',
                    'GET',
                    null,
                    { 'Authorization': `Bearer ${token}` }
                );

                if (result.success) {
                    log(`✅ Notificaciones pendientes obtenidas (${result.responseTime}ms)`, 'notificaciones-result', 'success');
                    log(`📋 Total pendientes: ${result.data.notificaciones.length}`, 'notificaciones-result', 'info');
                } else {
                    log(`❌ Error: ${result.error}`, 'notificaciones-result', 'error');
                }
            } catch (error) {
                log(`❌ Error de autenticación: ${error.message}`, 'notificaciones-result', 'error');
            }
        }

        // 5. FLUJO COMPLETO DE PROCESAMIENTO
        async function testFlujoCompleto() {
            log('🚀 Iniciando test de flujo completo...', 'flujo-result');

            const mensajes = [
                {
                    from: "+50312345678",
                    body: "Ausente: Pedro González, 1°A por enfermedad",
                    timestamp: new Date().toISOString()
                },
                {
                    from: "+50387654321",
                    body: "EMERGENCIA: Accidente en casa, necesito ayuda urgente",
                    timestamp: new Date().toISOString()
                },
                {
                    from: "+50398765432",
                    body: "¿Cuáles son las calificaciones de mi hija en matemáticas?",
                    timestamp: new Date().toISOString()
                }
            ];

            log(`📱 Enviando ${mensajes.length} mensajes de prueba...`, 'flujo-result', 'info');

            for (let i = 0; i < mensajes.length; i++) {
                const mensaje = mensajes[i];
                log(`📤 Enviando mensaje ${i + 1}: ${mensaje.body.substring(0, 50)}...`, 'flujo-result');

                const result = await makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje);

                if (result.success) {
                    log(`✅ Mensaje ${i + 1} procesado (${result.responseTime}ms)`, 'flujo-result', 'success');
                } else {
                    log(`❌ Error en mensaje ${i + 1}: ${result.error}`, 'flujo-result', 'error');
                }

                // Esperar un poco entre mensajes
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            log('🎉 Flujo completo finalizado', 'flujo-result', 'success');
        }

        async function testMultiplesMensajes() {
            log('📱 Probando múltiples mensajes simultáneos...', 'flujo-result');

            const mensajes = [
                { from: "+50311111111", body: "Hola, buenos días" },
                { from: "+50322222222", body: "Ausente: Ana María, 2°B" },
                { from: "+50333333333", body: "¿Cuándo es la reunión?" },
                { from: "+50344444444", body: "Necesito información de pagos" },
                { from: "+50355555555", body: "¿Qué tareas hay para hoy?" }
            ];

            const startTime = Date.now();

            const promises = mensajes.map(mensaje =>
                makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', {
                    ...mensaje,
                    timestamp: new Date().toISOString()
                })
            );

            const results = await Promise.all(promises);
            const endTime = Date.now();
            const totalTime = endTime - startTime;

            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;

            log(`✅ Procesados: ${successful}/${mensajes.length} mensajes`, 'flujo-result', 'success');
            log(`❌ Fallidos: ${failed}`, 'flujo-result', failed > 0 ? 'error' : 'info');
            log(`⚡ Tiempo total: ${totalTime}ms`, 'flujo-result', 'info');
            log(`📊 Promedio por mensaje: ${Math.round(totalTime / mensajes.length)}ms`, 'flujo-result', 'info');
        }

        async function testRendimiento() {
            log('⚡ Iniciando test de rendimiento...', 'flujo-result');

            const numMensajes = 10;
            const mensajes = [];

            for (let i = 0; i < numMensajes; i++) {
                mensajes.push({
                    from: `+5031234567${i.toString().padStart(2, '0')}`,
                    body: `Mensaje de prueba ${i + 1}: Hola, ¿cómo están?`,
                    timestamp: new Date().toISOString()
                });
            }

            log(`🚀 Enviando ${numMensajes} mensajes para test de rendimiento...`, 'flujo-result', 'info');

            const startTime = Date.now();
            const promises = mensajes.map(mensaje =>
                makeRequest('http://localhost:5678/webhook/whatsapp-webhook', 'POST', mensaje)
            );

            const results = await Promise.all(promises);
            const endTime = Date.now();

            const totalTime = endTime - startTime;
            const successful = results.filter(r => r.success).length;
            const avgResponseTime = results
                .filter(r => r.success)
                .reduce((sum, r) => sum + r.responseTime, 0) / successful;

            log(`📊 Resultados del test de rendimiento:`, 'flujo-result', 'success');
            log(`   • Mensajes procesados: ${successful}/${numMensajes}`, 'flujo-result', 'info');
            log(`   • Tiempo total: ${totalTime}ms`, 'flujo-result', 'info');
            log(`   • Tiempo promedio por mensaje: ${Math.round(avgResponseTime)}ms`, 'flujo-result', 'info');
            log(`   • Mensajes por segundo: ${Math.round((successful * 1000) / totalTime)}`, 'flujo-result', 'info');
            log(`   • Tasa de éxito: ${Math.round((successful / numMensajes) * 100)}%`, 'flujo-result', 'info');
        }

        function limpiarTodo() {
            document.getElementById('patrones-result').innerHTML = '';
            document.getElementById('respuestas-result').innerHTML = '';
            document.getElementById('reportes-result').innerHTML = '';
            document.getElementById('notificaciones-result').innerHTML = '';
            document.getElementById('flujo-result').innerHTML = '';

            testsTotal = 0;
            testsSuccess = 0;
            testsFailed = 0;
            responseTimes = [];
            updateStats();

            log('🧹 Todos los resultados han sido limpiados', 'flujo-result', 'info');
        }

        // Funciones de respuestas sofisticadas (placeholder)
        async function testRespuestaContextual() {
            log('💬 Test de respuesta contextual - Implementar según necesidades', 'respuestas-result', 'info');
        }

        async function testRespuestaUrgente() {
            log('⚡ Test de respuesta urgente - Implementar según necesidades', 'respuestas-result', 'info');
        }

        async function testRespuestaPersonalizada() {
            log('👤 Test de respuesta personalizada - Implementar según necesidades', 'respuestas-result', 'info');
        }

        async function testRespuestaMultiple() {
            log('🔄 Test de respuestas múltiples - Implementar según necesidades', 'respuestas-result', 'info');
        }

        // Auto-inicialización
        window.onload = function() {
            log('🚀 Sistema de pruebas avanzado iniciado', 'flujo-result', 'success');
            log('📋 Selecciona las pruebas que deseas ejecutar', 'flujo-result', 'info');
            updateStats();
        };
    </script>
</body>
</html>