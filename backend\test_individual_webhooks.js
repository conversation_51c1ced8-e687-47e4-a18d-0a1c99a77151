const axios = require('axios');

console.log('🧪 Probando webhooks individuales...');

// Función para probar un webhook específico
async function testWebhook(url, data, description) {
  try {
    console.log(`\n📡 Probando: ${description}`);
    console.log(`🔗 URL: ${url}`);
    
    const response = await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });
    
    console.log(`✅ Respuesta exitosa (${response.status})`);
    console.log(`📥 Respuesta:`, JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response) {
      console.log(`📥 Status: ${error.response.status}`);
      console.log(`📥 Respuesta de error:`, JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

// Función principal
async function main() {
  console.log('🚀 Iniciando pruebas individuales de webhooks...\n');
  
  // Test 1: Webhook que no requiere PostgreSQL (el que ya funcionaba)
  console.log('📋 TEST 1: Webhook existente (sin PostgreSQL)');
  const existingWebhook = {
    from: '50312345678',
    body: 'Mensaje de prueba',
    name: 'Usuario Prueba'
  };
  
  await testWebhook(
    'http://localhost:5678/webhook/whatsapp-webhook', 
    existingWebhook, 
    'Webhook existente que ya funcionaba'
  );
  
  // Test 2: Webhook principal con IA
  console.log('\n📋 TEST 2: Webhook principal con IA');
  const mainMessage = {
    from: '50312345678',
    body: 'Buenos días, mi hijo Juan Pérez no asistirá hoy',
    name: 'María González'
  };
  
  await testWebhook(
    'http://localhost:5678/webhook/whatsapp-main', 
    mainMessage, 
    'Webhook principal con análisis de IA'
  );
  
  // Test 3: Webhook de ausencias
  console.log('\n📋 TEST 3: Webhook de ausencias');
  const absenceData = {
    student_name: 'Juan Pérez',
    grade: '5to',
    reason: 'Enfermedad',
    sender_phone: '50312345678',
    sender_name: 'María González'
  };
  
  await testWebhook(
    'http://localhost:5678/webhook/process-absence', 
    absenceData, 
    'Webhook de procesamiento de ausencias'
  );
  
  // Test 4: Webhook de reportes
  console.log('\n📋 TEST 4: Webhook de reportes');
  const reportData = {
    student_name: 'Ana López',
    grade: '3ro',
    description: 'Problema de comportamiento',
    severity: 'medium',
    sender_phone: '50398765432',
    sender_name: 'Patricia López'
  };
  
  await testWebhook(
    'http://localhost:5678/webhook/behavior-report', 
    reportData, 
    'Webhook de reportes de comportamiento'
  );
  
  console.log('\n📝 Instrucciones para configurar credenciales PostgreSQL en n8n:');
  console.log('1. Acceder a: http://localhost:5678');
  console.log('2. Ir a Settings > Credentials');
  console.log('3. Crear nueva credencial tipo "PostgreSQL"');
  console.log('4. Configurar:');
  console.log('   - Host: postgres');
  console.log('   - Port: 5432');
  console.log('   - Database: ccjapdb');
  console.log('   - User: ccjapuser');
  console.log('   - Password: ccjappassword');
  console.log('   - SSL: Disable');
  console.log('5. Guardar con nombre: "PostgreSQL Local"');
  console.log('6. Editar cada workflow y asignar esta credencial a los nodos PostgreSQL');
  
  console.log('\n🔍 Para verificar datos en la base de datos:');
  console.log('docker exec -it ccjapdocenteautomatizacion-postgres-1 psql -U ccjapuser -d ccjapdb -c "SELECT * FROM mensajes_whatsapp ORDER BY fecha_recepcion DESC LIMIT 5;"');
}

// Ejecutar
main();
