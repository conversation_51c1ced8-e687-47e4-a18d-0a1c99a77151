const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🚀 Creando workflow de gestión de ausencias...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow para gestión de ausencias
async function createAbsenceWorkflow() {
  console.log('\n📋 Creando workflow de gestión de ausencias...');
  
  const workflowData = {
    name: 'WhatsApp IA - Gestión de Ausencias',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para procesar ausencias
      {
        parameters: {
          httpMethod: 'POST',
          path: 'process-absence',
          responseMode: 'responseNode'
        },
        id: 'absence-webhook',
        name: 'Trigger Ausencia',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Buscar estudiante en base de datos
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              nombre_completo: '={{ $json.student_name }}'
            }
          },
          table: 'alumnos',
          limit: 1
        },
        id: 'find-student',
        name: 'Buscar Estudiante',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 3. Crear registro de ausencia
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              alumno_id: '={{ $("Buscar Estudiante").first().json.id }}',
              fecha_ausencia: '={{ new Date().toISOString().split("T")[0] }}',
              motivo: '={{ $("Trigger Ausencia").first().json.reason }}',
              justificado: true,
              reportado_por_telefono: '={{ $("Trigger Ausencia").first().json.sender_phone }}',
              reportado_por_nombre: '={{ $("Trigger Ausencia").first().json.sender_name }}',
              procesado_por_ia: true,
              notificado_docente: false
            }
          },
          table: 'ausencias'
        },
        id: 'create-absence',
        name: 'Crear Ausencia',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [680, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 4. Notificar al director
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              tipo_notificacion: 'ausencia_reportada',
              titulo: 'Nueva Ausencia Reportada',
              mensaje: 'Se ha reportado una ausencia vía WhatsApp: {{ $("Buscar Estudiante").first().json.nombre_completo }}',
              destinatario_id: 1,
              prioridad: 'normal',
              datos_adicionales: '={{ JSON.stringify($json) }}'
            }
          },
          table: 'notificaciones_dashboard'
        },
        id: 'notify-director',
        name: 'Notificar Director',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [900, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 5. Preparar respuesta de confirmación
      {
        parameters: {
          jsCode: `
// Preparar respuesta de confirmación
const studentData = $('Buscar Estudiante').first().json;
const absenceData = $('Trigger Ausencia').first().json;

const mensaje = \`✅ *Ausencia Registrada*

Hemos registrado la ausencia de *\${studentData.nombre_completo}* para el día de hoy.

📋 El docente ha sido notificado automáticamente.
📞 Si necesita más información, puede contactarnos.

¡Que se mejore pronto! 🙏\`;

return [{
  json: {
    phone: absenceData.sender_phone,
    message: mensaje,
    student_name: studentData.nombre_completo,
    status: 'success'
  }
}];
`
        },
        id: 'prepare-confirmation',
        name: 'Preparar Confirmación',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1120, 300]
      },
      
      // 6. Respuesta del webhook
      {
        parameters: {
          options: {}
        },
        id: 'webhook-response',
        name: 'Respuesta Webhook',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [1340, 300]
      }
    ],
    
    connections: {
      'Trigger Ausencia': {
        main: [
          [
            {
              node: 'Buscar Estudiante',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Buscar Estudiante': {
        main: [
          [
            {
              node: 'Crear Ausencia',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Crear Ausencia': {
        main: [
          [
            {
              node: 'Notificar Director',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Notificar Director': {
        main: [
          [
            {
              node: 'Preparar Confirmación',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Confirmación': {
        main: [
          [
            {
              node: 'Respuesta Webhook',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow de ausencias creado con ID: ${result.id}`);
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow de ausencias:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow de ausencias
    const absenceWorkflowId = await createAbsenceWorkflow();
    
    console.log('\n🎉 ¡Workflow de ausencias creado exitosamente!');
    console.log('📋 Resumen:');
    console.log(`   - Workflow Ausencias: ${absenceWorkflowId}`);
    console.log('\n🔗 URLs de Webhook:');
    console.log(`   - Ausencias: ${N8N_BASE_URL}/webhook/process-absence`);
    console.log('\n📝 Próximos pasos:');
    console.log('   1. Acceder a n8n: http://localhost:5678');
    console.log('   2. Verificar que el workflow esté activo');
    console.log('   3. Configurar credenciales de PostgreSQL si es necesario');
    console.log('   4. Probar enviando datos de ausencia al webhook');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
