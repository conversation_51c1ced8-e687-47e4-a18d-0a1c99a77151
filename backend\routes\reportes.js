const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware } = require('../middleware/authMiddleware');

// Crear reporte automático de ausencias
router.post('/ausencias', async (req, res) => {
  try {
    const {
      tipo,
      estudiante,
      grado,
      seccion,
      motivo,
      fecha,
      hora,
      telefono_padre,
      prioridad,
      requiere_seguimiento,
      acciones_sugeridas
    } = req.body;

    // Insertar reporte en la base de datos
    const query = `
      INSERT INTO reportes_whatsapp_ausencias
      (estudiante, grado, seccion, motivo, fecha, hora, telefono_padre, prioridad, requiere_seguimiento, acciones_sugeridas, institucion_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const values = [
      estudiante,
      grado,
      seccion,
      motivo || 'No especificado',
      fecha,
      hora,
      telefono_padre,
      prioridad || 'normal',
      requiere_seguimiento || false,
      JSON.stringify(acciones_sugeridas || []),
      1 // institucion_id por defecto
    ];

    const result = await db.query(query, values);
    
    console.log(`📊 Reporte de ausencia creado: ${estudiante} - ${grado}°${seccion}`);
    
    res.json({
      success: true,
      message: 'Reporte de ausencia creado exitosamente',
      reporte: result.rows[0]
    });

  } catch (error) {
    console.error('Error creando reporte de ausencia:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// Obtener reportes de ausencias
router.get('/ausencias', authMiddleware, async (req, res) => {
  try {
    const { fecha, grado, seccion } = req.query;
    
    let query = `
      SELECT * FROM reportes_whatsapp_ausencias
      WHERE institucion_id = $1
    `;
    let values = [req.user.institucion_id];
    let paramCount = 1;

    if (fecha) {
      paramCount++;
      query += ` AND fecha = $${paramCount}`;
      values.push(fecha);
    }

    if (grado) {
      paramCount++;
      query += ` AND grado = $${paramCount}`;
      values.push(grado);
    }

    if (seccion) {
      paramCount++;
      query += ` AND seccion = $${paramCount}`;
      values.push(seccion);
    }

    query += ` ORDER BY fecha DESC, hora DESC`;

    const result = await db.query(query, values);
    
    res.json({
      success: true,
      reportes: result.rows
    });

  } catch (error) {
    console.error('Error obteniendo reportes:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor'
    });
  }
});

// Generar reporte diario de ausencias
router.get('/ausencias/diario', authMiddleware, async (req, res) => {
  try {
    const fecha = req.query.fecha || new Date().toISOString().split('T')[0];
    
    const query = `
      SELECT 
        grado,
        seccion,
        COUNT(*) as total_ausencias,
        STRING_AGG(estudiante, ', ') as estudiantes_ausentes,
        STRING_AGG(motivo, ', ') as motivos
      FROM reportes_whatsapp_ausencias
      WHERE fecha = $1 AND institucion_id = $2
      GROUP BY grado, seccion
      ORDER BY grado, seccion
    `;

    const result = await db.query(query, [fecha, req.user.institucion_id]);
    
    // Calcular estadísticas
    const totalAusencias = result.rows.reduce((sum, row) => sum + parseInt(row.total_ausencias), 0);
    const gradosAfectados = [...new Set(result.rows.map(row => row.grado))].length;
    
    res.json({
      success: true,
      fecha: fecha,
      resumen: {
        total_ausencias: totalAusencias,
        grados_afectados: gradosAfectados,
        secciones_afectadas: result.rows.length
      },
      detalle_por_grado: result.rows
    });

  } catch (error) {
    console.error('Error generando reporte diario:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor'
    });
  }
});

// Generar reporte semanal de ausencias
router.get('/ausencias/semanal', authMiddleware, async (req, res) => {
  try {
    const fechaFin = req.query.fecha_fin || new Date().toISOString().split('T')[0];
    const fechaInicio = new Date(fechaFin);
    fechaInicio.setDate(fechaInicio.getDate() - 6);
    
    const query = `
      SELECT 
        fecha,
        COUNT(*) as total_ausencias,
        COUNT(CASE WHEN motivo ILIKE '%medic%' OR motivo ILIKE '%doctor%' THEN 1 END) as ausencias_medicas,
        COUNT(CASE WHEN prioridad = 'alta' THEN 1 END) as ausencias_urgentes
      FROM reportes_whatsapp_ausencias
      WHERE fecha BETWEEN $1 AND $2 AND institucion_id = $3
      GROUP BY fecha
      ORDER BY fecha
    `;

    const result = await db.query(query, [fechaInicio.toISOString().split('T')[0], fechaFin, req.user.institucion_id]);
    
    res.json({
      success: true,
      periodo: {
        fecha_inicio: fechaInicio.toISOString().split('T')[0],
        fecha_fin: fechaFin
      },
      datos_diarios: result.rows
    });

  } catch (error) {
    console.error('Error generando reporte semanal:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor'
    });
  }
});

module.exports = router;
