const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🚀 Creando workflow de IA simplificado (sin PostgreSQL)...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow de IA simplificado
async function createSimpleAIWorkflow() {
  console.log('\n📋 Creando workflow de IA simplificado...');
  
  const workflowData = {
    name: 'WhatsApp IA - Simple (Sin BD)',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir mensajes
      {
        parameters: {
          httpMethod: 'POST',
          path: 'ai-simple',
          responseMode: 'responseNode'
        },
        id: 'webhook-ai',
        name: 'WhatsApp IA Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Análisis con IA (simulado)
      {
        parameters: {
          jsCode: `
// Análisis inteligente del mensaje
const mensaje = $input.first().json.body || '';
const telefono = $input.first().json.from || '';
const nombre = $input.first().json.name || telefono;

console.log('📨 Mensaje recibido:', mensaje);
console.log('📞 De:', nombre, '(' + telefono + ')');

// Palabras clave para detectar tipos de mensaje
const palabrasAusencia = ['ausencia', 'no asistirá', 'no va', 'enfermo', 'enfermedad', 'falta', 'faltar', 'no viene', 'no vendrá'];
const palabrasConsulta = ['horario', 'cuando', 'qué hora', 'información', 'pregunta', '¿', 'hora', 'tiempo'];
const palabrasReporte = ['reporte', 'problema', 'comportamiento', 'incidente', 'queja', 'mal comportamiento', 'indisciplina'];
const palabrasUrgente = ['urgente', 'emergencia', 'grave', 'serio', 'importante', 'director'];

let tipoMensaje = 'general';
let confianza = 0.5;
let respuestaAutomatica = 'Gracias por contactarnos. Su mensaje ha sido recibido y será procesado.';
let requiereAprobacion = false;
let autoEnvio = true;

// Detectar tipo de mensaje
const mensajeLower = mensaje.toLowerCase();

// Detectar ausencias
if (palabrasAusencia.some(palabra => mensajeLower.includes(palabra))) {
  tipoMensaje = 'ausencia';
  confianza = 0.95;
  autoEnvio = true;
  requiereAprobacion = false;
  
  // Extraer nombre del estudiante si es posible
  const nombreMatch = mensaje.match(/mi hijo[a]?\\s+([A-Za-zÁÉÍÓÚáéíóúñÑ\\s]+)/i);
  const estudianteNombre = nombreMatch ? nombreMatch[1].trim() : 'su hijo/a';
  
  respuestaAutomatica = \`✅ *Ausencia Registrada*

Hemos registrado el reporte de ausencia de *\${estudianteNombre}* para el día de hoy.

📋 El docente será notificado automáticamente.
📞 Si necesita más información, puede contactarnos al horario de oficina.

¡Que se mejore pronto! 🙏

_Mensaje procesado automáticamente por IA_\`;

} else if (palabrasConsulta.some(palabra => mensajeLower.includes(palabra))) {
  tipoMensaje = 'consulta';
  confianza = 0.85;
  autoEnvio = true;
  requiereAprobacion = false;
  
  respuestaAutomatica = \`📚 *Información del Colegio*

🕐 *Horarios:*
• Clases: 7:00 AM - 12:00 PM
• Oficina: 7:00 AM - 3:00 PM
• Lunes a Viernes

📞 *Contacto:*
• Teléfono: +503 1234-5678
• WhatsApp: Este número

📍 *Ubicación:*
Colegio Cristiano Jerusalén de los Altos de Palencia

Si necesita información específica, por favor especifique su consulta.

_Respuesta automática generada por IA_\`;

} else if (palabrasReporte.some(palabra => mensajeLower.includes(palabra))) {
  tipoMensaje = 'reporte_comportamiento';
  confianza = 0.90;
  autoEnvio = false;
  requiereAprobacion = true;
  
  // Detectar urgencia
  const esUrgente = palabrasUrgente.some(palabra => mensajeLower.includes(palabra));
  
  respuestaAutomatica = \`📋 *Reporte Recibido*

Hemos recibido su reporte sobre comportamiento estudiantil.

\${esUrgente ? '🚨 *MARCADO COMO URGENTE*' : '⏳ En proceso de revisión'}

👨‍💼 El director revisará la información proporcionada y se pondrá en contacto con usted.

📞 Para casos urgentes, puede llamar directamente a la oficina.

¡Gracias por mantenernos informados!

_Reporte procesado por IA - Requiere aprobación del director_\`;

} else {
  // Mensaje general
  respuestaAutomatica = \`👋 *Hola \${nombre}*

Gracias por contactar al Colegio Cristiano Jerusalén.

Su mensaje ha sido recibido y será atendido por nuestro personal.

🕐 *Horario de atención:*
Lunes a Viernes: 7:00 AM - 3:00 PM

📞 Para urgencias, puede llamar directamente a la oficina.

_Respuesta automática generada por IA_\`;
}

// Preparar respuesta estructurada
const analisisCompleto = {
  mensaje_original: mensaje,
  remitente: {
    telefono: telefono,
    nombre: nombre
  },
  analisis_ia: {
    tipo_mensaje: tipoMensaje,
    confianza: confianza,
    requiere_aprobacion: requiereAprobacion,
    auto_envio: autoEnvio,
    timestamp: new Date().toISOString()
  },
  respuesta_automatica: respuestaAutomatica,
  acciones_recomendadas: {
    notificar_docente: tipoMensaje === 'ausencia',
    notificar_director: tipoMensaje === 'reporte_comportamiento' || palabrasUrgente.some(p => mensajeLower.includes(p)),
    envio_inmediato: autoEnvio
  }
};

console.log('🤖 Análisis IA completado:', JSON.stringify(analisisCompleto, null, 2));

return [{
  json: analisisCompleto
}];
`
        },
        id: 'ai-analysis',
        name: 'Análisis IA Avanzado',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [460, 300]
      },
      
      // 3. Preparar respuesta final
      {
        parameters: {
          jsCode: `
// Preparar respuesta final para WhatsApp
const data = $input.first().json;

const respuestaFinal = {
  success: true,
  message: "Mensaje procesado exitosamente",
  analysis: {
    type: data.analisis_ia.tipo_mensaje,
    confidence: data.analisis_ia.confianza,
    auto_response: data.respuesta_automatica
  },
  sender: data.remitente,
  timestamp: new Date().toISOString(),
  processed_by: "n8n-ai-system"
};

console.log('📤 Respuesta final preparada:', JSON.stringify(respuestaFinal, null, 2));

return [{
  json: respuestaFinal
}];
`
        },
        id: 'prepare-final-response',
        name: 'Preparar Respuesta Final',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [680, 300]
      },
      
      // 4. Respuesta del webhook
      {
        parameters: {
          options: {}
        },
        id: 'webhook-response',
        name: 'Respuesta Webhook',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [900, 300]
      }
    ],
    
    connections: {
      'WhatsApp IA Webhook': {
        main: [
          [
            {
              node: 'Análisis IA Avanzado',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Análisis IA Avanzado': {
        main: [
          [
            {
              node: 'Preparar Respuesta Final',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Respuesta Final': {
        main: [
          [
            {
              node: 'Respuesta Webhook',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow de IA simplificado creado con ID: ${result.id}`);
    
    // Activar el workflow
    await makeN8nRequest('POST', `/workflows/${result.id}/activate`);
    console.log(`✅ Workflow activado automáticamente`);
    
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow de IA:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow de IA simplificado
    const aiWorkflowId = await createSimpleAIWorkflow();
    
    console.log('\n🎉 ¡Workflow de IA creado y activado exitosamente!');
    console.log('📋 Resumen:');
    console.log(`   - Workflow IA Simple: ${aiWorkflowId}`);
    console.log('\n🔗 URL de Webhook:');
    console.log(`   - IA Simple: ${N8N_BASE_URL}/webhook/ai-simple`);
    
    console.log('\n🧪 Probar el sistema:');
    console.log('curl -X POST http://localhost:5678/webhook/ai-simple \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"from": "50312345678", "body": "Mi hijo Juan no asistirá hoy por enfermedad", "name": "María González"}\'');
    
    console.log('\n📝 Tipos de mensajes que puede procesar:');
    console.log('✅ Ausencias: "Mi hijo no asistirá hoy por enfermedad"');
    console.log('✅ Consultas: "¿A qué hora salen los estudiantes?"');
    console.log('✅ Reportes: "Quiero reportar un problema de comportamiento"');
    console.log('✅ Generales: Cualquier otro mensaje');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
