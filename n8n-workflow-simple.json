{"name": "Procesamiento Avanzado de Mensajes WhatsApp", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"httpMethod": "POST", "path": "whatsapp-webhook", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300], "webhookId": "whatsapp-webhook"}, {"parameters": {"functionCode": "const message = $input.first();\nconst body = String(message.json.body || message.json.text || '').toLowerCase();\nconst originalBody = String(message.json.body || message.json.text || '');\nconst from = String(message.json.from || '');\nconst timestamp = message.json.timestamp || new Date().toISOString();\n\nlet messageType = 'OTRO';\nlet responseMessage = '';\nlet isAbsence = false;\nlet studentName = '';\nlet grade = '';\nlet section = '';\nlet priority = 'normal';\n\nif (body && typeof body === 'string') {\n  // Detectar ausencias\n  const absenceMatch = originalBody.match(/(?:ausente|no\\s*asist|falta)[\\s:]*([^,\\n]+)(?:[,\\s]*(?:grado|°)?\\s*(\\d+)\\s*([a-z]?))?/i);\n  if (absenceMatch) {\n    messageType = 'AUSENCIA';\n    isAbsence = true;\n    studentName = (absenceMatch[1] || '').trim();\n    grade = absenceMatch[2] || '';\n    section = (absenceMatch[3] || 'A').toUpperCase();\n    responseMessage = `Ausencia registrada: ${studentName}${grade ? ` (${grade}°${section})` : ''}`;\n  }\n  // Detectar emergencias\n  else if (/emergencia|urgente|accidente/i.test(body)) {\n    messageType = 'EMERGENCIA';\n    priority = 'alta';\n    responseMessage = 'EMERGENCIA detectada. Personal sera contactado inmediatamente.';\n  }\n  // Detectar consultas\n  else if (/calificaci|nota|tarea|horario/i.test(body)) {\n    messageType = 'CONSULTA';\n    responseMessage = 'Consulta recibida. Responderemos en breve.';\n  }\n  // Saludos\n  else if (/hola|buen/i.test(body)) {\n    messageType = 'SALUDO';\n    responseMessage = 'Hola! Bienvenido al sistema CCJAP. ¿En que puedo ayudarte?';\n  }\n}\n\nconst payload = {\n  from: from,\n  text: originalBody,\n  messageType: messageType,\n  studentName: studentName,\n  grade: grade,\n  section: section,\n  isAbsence: isAbsence,\n  priority: priority,\n  timestamp: timestamp\n};\n\nif (responseMessage) {\n  payload.response = {\n    message: responseMessage,\n    type: 'AUTOMATIC_REPLY'\n  };\n}\n\nreturn { json: payload };"}, "name": "Procesar <PERSON><PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [700, 300]}, {"parameters": {"requestMethod": "POST", "url": "http://backend:3001/api/webhook/whatsapp", "options": {}}, "name": "Enviar a Backend", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [950, 300]}, {"parameters": {"options": {}}, "name": "Respuesta Exitosa", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1200, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Procesar <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Procesar Mensaje Avanzado": {"main": [[{"node": "Enviar a Backend", "type": "main", "index": 0}]]}, "Enviar a Backend": {"main": [[{"node": "Respuesta Exitosa", "type": "main", "index": 0}]]}}, "settings": {}}