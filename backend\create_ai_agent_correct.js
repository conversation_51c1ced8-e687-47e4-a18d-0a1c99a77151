const axios = require('axios');

// Configuración para desarrollo local
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlYjkyNWNjYy1mNzZiLTQxZjItODYwNS0wY2EyMTI4OTVlOTkiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ5MzY4NjEzfQ.qE2SuiglknsLoRvmeukpu58u2X67TDlwevvvmtg5-gU';

console.log('🤖 Creando workflow con AI Agent correcto...');

// Función para hacer peticiones a n8n
async function makeN8nRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${N8N_BASE_URL}/api/v1${endpoint}`,
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`📡 ${method} ${endpoint}`);
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Error en petición n8n ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Workflow con AI Agent correcto
async function createCorrectAIAgentWorkflow() {
  console.log('\n🧠 Creando workflow con AI Agent correcto...');
  
  const workflowData = {
    name: 'WhatsApp AI Agent - Memoria y ChatGPT',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir mensajes
      {
        parameters: {
          httpMethod: 'POST',
          path: 'ai-agent-chat',
          responseMode: 'responseNode'
        },
        id: 'webhook-chat',
        name: 'WhatsApp Chat Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },
      
      // 2. Preparar datos para AI Agent
      {
        parameters: {
          jsCode: `
// Preparar datos para el AI Agent
const inputData = $input.first().json;

// Extraer información del mensaje
const mensaje = inputData.body || inputData.chatInput || inputData.message || '';
const telefono = inputData.from || inputData.phone || 'unknown';
const nombre = inputData.name || 'Usuario';
const sessionId = telefono; // Usar teléfono como session ID para memoria

// Preparar formato para AI Agent
const chatData = {
  chatInput: mensaje,
  sessionId: sessionId,
  from: telefono,
  name: nombre,
  timestamp: new Date().toISOString(),
  type: inputData.type || 'text'
};

console.log('📝 Datos preparados para AI Agent:', JSON.stringify(chatData, null, 2));

return [{
  json: chatData
}];
`
        },
        id: 'prepare-chat-data',
        name: 'Preparar Datos Chat',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [460, 300]
      },
      
      // 3. AI Agent con memoria
      {
        parameters: {
          agent: 'conversationalAgent',
          promptType: 'define',
          text: `Eres el asistente inteligente del Colegio Cristiano Jerusalén de los Altos de Palencia (CCJAP).

INFORMACIÓN DEL COLEGIO:
- Nombre: Colegio Cristiano Jerusalén de los Altos de Palencia
- Horario de clases: 7:00 AM - 12:00 PM (Lunes a Viernes)
- Horario de oficina: 7:00 AM - 3:00 PM (Lunes a Viernes)
- Teléfono: +503 1234-5678
- Ubicación: Palencia, El Salvador

CAPACIDADES PRINCIPALES:
1. AUSENCIAS: Procesar reportes de inasistencia estudiantil
2. CONSULTAS: Responder sobre horarios, actividades, información general
3. REPORTES: Gestionar reportes de comportamiento con evidencia
4. COMUNICACIÓN: Facilitar comunicación entre padres, docentes y dirección

TIPOS DE MENSAJE A CLASIFICAR:
- "ausencia": Reporte de inasistencia estudiantil
- "consulta": Pregunta sobre información del colegio
- "reporte_comportamiento": Reporte de comportamiento con/sin evidencia
- "director_atencion": Requiere intervención urgente del director
- "general": Mensaje general o saludo

INSTRUCCIONES:
- Siempre responde de forma profesional, empática y útil
- Para ausencias: confirma recepción y notifica que se informará al docente
- Para consultas: proporciona información precisa del colegio
- Para reportes: confirma recepción y explica el proceso de revisión
- Mantén un tono cálido pero profesional
- Usa emojis apropiados para hacer la comunicación más amigable
- Si no entiendes algo, pide aclaración de forma cortés
- Recuerda conversaciones anteriores y haz referencia a ellas cuando sea apropiado

EJEMPLOS DE RESPUESTAS:

Para ausencias:
"✅ He registrado la ausencia de [nombre del estudiante]. El docente será notificado automáticamente. ¿Necesita que le proporcione alguna información adicional sobre tareas o actividades perdidas?"

Para consultas:
"📚 Con gusto le ayudo con esa información. [Respuesta específica]. ¿Hay algo más en lo que pueda asistirle?"

Para reportes:
"📋 He recibido su reporte sobre [situación]. La dirección revisará la información y se pondrá en contacto con usted dentro de las próximas 24 horas. ¿Hay algún detalle adicional que considere importante agregar?"

Mantén siempre la memoria de la conversación y personaliza las respuestas según el contexto previo.`,
          options: {
            systemMessage: 'Tienes acceso a la memoria de conversaciones anteriores. Úsala para proporcionar respuestas más personalizadas y contextuales. Recuerda el nombre del usuario y cualquier información relevante de conversaciones previas.'
          }
        },
        id: 'ai-agent-ccjap',
        name: 'AI Agent CCJAP',
        type: 'n8n-nodes-langchain.agent',
        typeVersion: 1,
        position: [680, 300]
      },
      
      // 4. Procesar respuesta del AI Agent
      {
        parameters: {
          jsCode: `
// Procesar respuesta del AI Agent
const agentResponse = $input.first().json;
const originalData = $('Preparar Datos Chat').first().json;

// Extraer respuesta del agente
const respuestaIA = agentResponse.output || agentResponse.text || agentResponse.response || 'Sin respuesta del agente';

// Análisis del tipo de mensaje
const mensaje = originalData.chatInput.toLowerCase();
let tipoMensaje = 'general';
let confianza = 0.8;
let requiereAprobacion = false;

// Clasificación inteligente
if (mensaje.includes('ausencia') || mensaje.includes('no asistirá') || mensaje.includes('enfermo')) {
  tipoMensaje = 'ausencia';
  confianza = 0.95;
  requiereAprobacion = false;
} else if (mensaje.includes('horario') || mensaje.includes('información') || mensaje.includes('pregunta')) {
  tipoMensaje = 'consulta';
  confianza = 0.9;
  requiereAprobacion = false;
} else if (mensaje.includes('reporte') || mensaje.includes('problema') || mensaje.includes('comportamiento')) {
  tipoMensaje = 'reporte_comportamiento';
  confianza = 0.9;
  requiereAprobacion = true;
} else if (mensaje.includes('urgente') || mensaje.includes('director')) {
  tipoMensaje = 'director_atencion';
  confianza = 0.85;
  requiereAprobacion = true;
}

const analisisCompleto = {
  mensaje_original: originalData.chatInput,
  respuesta_ia: respuestaIA,
  clasificacion: {
    tipo_mensaje: tipoMensaje,
    confianza: confianza,
    requiere_aprobacion: requiereAprobacion,
    procesado_por: 'ai-agent-chatgpt'
  },
  remitente: {
    telefono: originalData.from,
    nombre: originalData.name,
    session_id: originalData.sessionId
  },
  timestamp: new Date().toISOString(),
  memoria_utilizada: true
};

console.log('🤖 Análisis AI Agent completado:', JSON.stringify(analisisCompleto, null, 2));

return [{
  json: analisisCompleto
}];
`
        },
        id: 'process-ai-response',
        name: 'Procesar Respuesta IA',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 300]
      },
      
      // 5. Guardar en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.remitente.telefono }}',
              nombre_remitente: '={{ $json.remitente.nombre }}',
              texto_mensaje: '={{ $json.mensaje_original }}',
              tipo_mensaje: '={{ $json.clasificacion.tipo_mensaje }}',
              ai_analysis: '={{ JSON.stringify($json) }}',
              confidence_score: '={{ $json.clasificacion.confianza }}',
              requires_approval: '={{ $json.clasificacion.requiere_aprobacion }}',
              response_message: '={{ $json.respuesta_ia }}',
              procesado: true,
              fecha_recepcion: '={{ new Date().toISOString() }}'
            }
          },
          table: 'mensajes_whatsapp'
        },
        id: 'save-to-db',
        name: 'Guardar en BD',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1120, 300],
        credentials: {
          postgres: {
            id: 'postgres-local',
            name: 'PostgreSQL Local'
          }
        }
      },
      
      // 6. Respuesta del webhook
      {
        parameters: {
          options: {}
        },
        id: 'webhook-response',
        name: 'Respuesta Final',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [1340, 300]
      }
    ],
    
    connections: {
      'WhatsApp Chat Webhook': {
        main: [
          [
            {
              node: 'Preparar Datos Chat',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Datos Chat': {
        main: [
          [
            {
              node: 'AI Agent CCJAP',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'AI Agent CCJAP': {
        main: [
          [
            {
              node: 'Procesar Respuesta IA',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar Respuesta IA': {
        main: [
          [
            {
              node: 'Guardar en BD',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Guardar en BD': {
        main: [
          [
            {
              node: 'Respuesta Final',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };
  
  try {
    const result = await makeN8nRequest('POST', '/workflows', workflowData);
    console.log(`✅ Workflow con AI Agent creado con ID: ${result.id}`);
    
    // Activar el workflow
    await makeN8nRequest('POST', `/workflows/${result.id}/activate`);
    console.log(`✅ Workflow activado automáticamente`);
    
    return result.id;
  } catch (error) {
    console.error('❌ Error creando workflow con AI Agent:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('🔍 Verificando conexión con n8n...');
    
    // Verificar conexión
    await makeN8nRequest('GET', '/workflows');
    console.log('✅ Conexión con n8n establecida');
    
    // Crear workflow con AI Agent
    const agentWorkflowId = await createCorrectAIAgentWorkflow();
    
    console.log('\n🎉 ¡Workflow con AI Agent creado exitosamente!');
    console.log('📋 Características implementadas:');
    console.log('   ✅ AI Agent con memoria de conversación');
    console.log('   ✅ ChatGPT integrado');
    console.log('   ✅ Análisis inteligente de mensajes');
    console.log('   ✅ Clasificación automática por tipo');
    console.log('   ✅ Almacenamiento en base de datos');
    console.log('   ✅ Session ID por teléfono para memoria');
    
    console.log('\n🔗 URL de Webhook:');
    console.log(`   - AI Agent Chat: ${N8N_BASE_URL}/webhook/ai-agent-chat`);
    
    console.log('\n🧪 Ejemplo de prueba:');
    console.log('curl -X POST http://localhost:5678/webhook/ai-agent-chat \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"body": "Mi hijo Juan no asistirá hoy por enfermedad", "from": "50312345678", "name": "María González"}\'');
    
    console.log('\n⚠️ IMPORTANTE:');
    console.log('1. El AI Agent necesita nodos adicionales conectados:');
    console.log('   - Chat Model (OpenAI)');
    console.log('   - Memory (Window Buffer)');
    console.log('2. Configurar credenciales OpenAI y PostgreSQL');
    console.log('3. Conectar los nodos manualmente en n8n');
    
    console.log('\n📝 PRÓXIMOS PASOS:');
    console.log('1. Ir a n8n: http://localhost:5678');
    console.log('2. Abrir el workflow creado');
    console.log('3. Agregar y conectar nodos de Chat Model y Memory');
    console.log('4. Configurar credenciales');
    console.log('5. Activar el workflow');
    
  } catch (error) {
    console.error('\n💥 Error en la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar
main();
